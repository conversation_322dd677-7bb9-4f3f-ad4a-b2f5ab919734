import React, {useEffect, useState} from 'react';
import MainContainer from '../../../components/base/main-container/main-container';
import {Icon, IconType} from '../../../components/base/icon/icon';
import {MapPageStyled} from './select-address.page.styled';
import {TextView} from '../../../components/base/text-view/text-view';
import {TextViewVariant} from '../../../components/base/text-view/constants';
import GooglePlacesAutoCompleteInput from '../../../components/base/map/google-autocomplete/google-autocomplete-input';
import Map from '../../../components/base/map/map/map';
import Button from '../../../components/base/button/button';
import {ButtonVariant} from '../../../components/base/button/constants';
import {strings} from '../../../translation/strings';
import {it} from '../../../translation/supportedlanguage';
import Geolocation from '@react-native-community/geolocation';
import {Alert, Platform, PermissionsAndroid} from 'react-native';
import {defaultCoords, initialCoords} from './constants';
import {AddressComponent} from 'react-native-google-places-autocomplete';
import {getFormattedAddressName} from '../../../utils';
import Geocoder from 'react-native-geocoding';

interface AddressData {
  street?: string;
  street_number?: string;
  city?: string;
  zip_code?: string;
  region?: string;
  country?: string;
  formattedAddress?: string;
}

interface Coords {
  latitude: number;
  longitude: number;
  addressData: AddressData;
}

const SelectAddressPage = (props: any) => {
  const [coords, setCoords] = useState<Coords>({
    latitude: initialCoords.latitude,
    longitude: initialCoords.longitude,
    addressData: {},
  });

  const onPressButton = (addressData: any) => {
    if (props.route.params.screenBottomTab) {
      props.navigation.navigate(props.route.params.callingPage, {
        params: {
          [props.route.params.fieldName]: addressData,
        },
        screen: props.route.params.screenBottomTab,
      });
    } else {
      props.navigation.navigate(props.route.params.callingPage, {
        [props.route.params.fieldName]: addressData,
      });
    }
  };

  const getFormattedAddress = (event: any, address: any) => {
    const formattedAddress = getFormattedAddressName(address, false);

    const addressData = {
      street: address.street,
      street_number: address.streetNumber,
      city: address.city,
      zip_code: address.postalCode,
      region: address.administrativeArea
        ? address.administrativeArea
        : address.region,
      country: address.country,
      formattedAddress: formattedAddress,
      subregion: address.subregion,
      latitude: event.latitude,
      longitude: event.longitude,
    };

    setCoords({
      latitude: event.latitude,
      longitude: event.longitude,
      addressData: addressData,
    });
  };

  const getLocation = async (event: any) => {
    Geolocation.requestAuthorization(
      () => {
        if (event.latitude && event.longitude) {
          Geocoder.from(event.latitude, event.longitude)
            .then((json: Geocoder.GeocoderResponse) => {
              const addressComponent = json.results[0];
              if (!addressComponent || !addressComponent.address_components) {
                Alert.alert(strings(it.errorOccurred));
                return;
              }
              if (
                addressComponent &&
                addressComponent.address_components[0].long_name &&
                addressComponent.address_components[2].long_name
              ) {
                getFormattedAddress(event, addressComponent.address_components);
              } else {
                Alert.alert(strings(it.errorSelectAValidAddress));
                setCoords({
                  latitude: defaultCoords.latitude,
                  longitude: defaultCoords.longitude,
                  addressData: coords.addressData,
                });
                setCoords(coords);
              }
            })
            .catch((error: any) => {
              console.warn(error);
              Alert.alert(strings(it.errorOccurred));
            });
        } else {
          Alert.alert(strings(it.errorOccurred));
        }
      },
      () => {
        Alert.alert(
          strings(it.attention),
          strings(it.errorMissedPermissionsPosition),
        );
        setCoords({
          latitude: initialCoords.latitude,
          longitude: initialCoords.longitude,
          addressData: {},
        });
        setCoords({
          latitude: defaultCoords.latitude,
          longitude: defaultCoords.longitude,
          addressData: {},
        });
      },
    );
  };
  const getCurrentUserPosition = async () => {
    try {
      if (Platform.OS === 'ios') {
        Geolocation.requestAuthorization(
          () => {
            Geolocation.getCurrentPosition(
              position => {
                const {latitude, longitude} = position.coords;
                getLocation({latitude, longitude});
              },
              error => {
                console.error('iOS getCurrentPosition error:', error);
                Alert.alert(
                  strings(it.attention),
                  strings(it.errorMissedPermissionsPosition),
                );
                setCoords({
                  ...coords,
                  latitude: defaultCoords.latitude,
                  longitude: defaultCoords.longitude,
                });
              },
              {enableHighAccuracy: true, timeout: 20000, maximumAge: 1000}
            );
          },
          error => {
            console.error('iOS location permission denied:', error);
            Alert.alert(
              strings(it.attention),
              strings(it.errorMissedPermissionsPosition),
            );
            setCoords({
              ...coords,
              latitude: defaultCoords.latitude,
              longitude: defaultCoords.longitude,
            });
          }
        );
      } else {
        const fineLocationGranted = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        );
        const coarseLocationGranted = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
        );

        if (fineLocationGranted || coarseLocationGranted) {
          Geolocation.getCurrentPosition(
            position => {
              const {latitude, longitude} = position.coords;
              getLocation({latitude, longitude});
            },
            error => {
              console.error('Android getCurrentPosition error:', error);
              Alert.alert(
                strings(it.attention),
                strings(it.errorMissedPermissionsPosition),
              );
              setCoords({
                ...coords,
                latitude: defaultCoords.latitude,
                longitude: defaultCoords.longitude,
              });
            },
            {enableHighAccuracy: true, timeout: 20000, maximumAge: 1000}
          );
        } else {
          const fine = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          );
          const coarse = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
          );

          const granted =
            fine === PermissionsAndroid.RESULTS.GRANTED ||
            coarse === PermissionsAndroid.RESULTS.GRANTED;

          if (granted) {
            Geolocation.getCurrentPosition(
              position => {
                const {latitude, longitude} = position.coords;
                getLocation({latitude, longitude});
              },
              error => {
                console.error('Android getCurrentPosition error after permission:', error);
                Alert.alert(
                  strings(it.attention),
                  strings(it.errorMissedPermissionsPosition),
                );
                setCoords({
                  ...coords,
                  latitude: defaultCoords.latitude,
                  longitude: defaultCoords.longitude,
                });
              },
              {enableHighAccuracy: true, timeout: 20000, maximumAge: 1000}
            );
          } else {
            Alert.alert(
              strings(it.attention),
              strings(it.errorMissedPermissionsPosition),
            );
            setCoords({
              ...coords,
              latitude: defaultCoords.latitude,
              longitude: defaultCoords.longitude,
            });
          }
        }
      }
    } catch (e) {
      Alert.alert(
        strings(it.attention),
        strings(it.errorMissedPermissionsPosition),
      );
      setCoords({
        ...coords,
        latitude: defaultCoords.latitude,
        longitude: defaultCoords.longitude,
      });
    }
  };
  useEffect(() => {
    getCurrentUserPosition().catch(error => console.log(error));
    return () => {
      setCoords({
        ...coords,
        latitude: defaultCoords.latitude,
        longitude: defaultCoords.longitude,
      });
    };
  }, []);

  return (
    <MainContainer>
      <MapPageStyled.Header>
        <MapPageStyled.ContainerLogo>
          <MapPageStyled.ContainerLogoSides>
            <MapPageStyled.ButtonBack onPress={() => props.navigation.goBack()}>
              <Icon type={IconType.ArrowLeft} />
            </MapPageStyled.ButtonBack>
          </MapPageStyled.ContainerLogoSides>
          <Icon type={IconType.Logo} />
          <MapPageStyled.ContainerLogoSides />
        </MapPageStyled.ContainerLogo>
        <TextView
          type={TextViewVariant.HeaderTitleDark}
          text={strings(it.selectAddressPage)}
        />
        <TextView
          type={TextViewVariant.SecondaryTitle}
          text={strings(it.selectAddressPageWhatYouCanDo)}
        />
        <MapPageStyled.GooglePlacesContainer>
          <GooglePlacesAutoCompleteInput
            onPress={(data, details) => {
              console.log('data', data);
              console.log('details', details);
              const addressData = {
                street: details.address_components.find(
                  (item: AddressComponent) => item.types.includes('route'),
                )?.long_name,
                street_number: details.address_components.find(
                  (item: AddressComponent) =>
                    item.types.includes('street_number'),
                )?.long_name,
                city:
                  details.address_components.find((item: AddressComponent) =>
                    item.types.includes('locality'),
                  )?.long_name ||
                  details.address_components.find((item: AddressComponent) =>
                    item.types.includes('administrative_area_level_3'),
                  )?.long_name,
                zip_code: details.address_components.find(
                  (item: AddressComponent) =>
                    item.types.includes('postal_code'),
                )?.long_name,
                region: details.address_components.find(
                  (item: AddressComponent) =>
                    item.types.includes('administrative_area_level_1'),
                )?.long_name,
                country: details.address_components.find(
                  (item: AddressComponent) => item.types.includes('country'),
                )?.long_name,
                subregion: details.address_components.find(
                  (item: AddressComponent) =>
                    item.types.includes('administrative_area_level_2'),
                )?.long_name,
                formattedAddress: details.formatted_address,
                latitude: details.geometry.location.lat,
                longitude: details.geometry.location.lng,
              };
              setCoords({
                latitude: details.geometry.location.lat,
                longitude: details.geometry.location.lng,
                addressData: addressData,
              });
            }}
            address={coords.addressData.formattedAddress}
          />
        </MapPageStyled.GooglePlacesContainer>
      </MapPageStyled.Header>
      <MapPageStyled.MapContainer>
        <Map
          coords={coords}
          onDragEnd={event => getLocation(event)}
          onLongPress={event => getLocation(event)}
        />
        <MapPageStyled.ButtonContainer>
          <Button
            label={strings(it.mapContinue)}
            onPress={() => onPressButton(coords.addressData)}
            type={ButtonVariant.Primary}
          />
        </MapPageStyled.ButtonContainer>
      </MapPageStyled.MapContainer>
    </MainContainer>
  );
};

export default SelectAddressPage;
