{"info": {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, "cxxBuildFolder": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\f75j4i29\\x86", "soFolder": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\f75j4i29\\obj\\x86", "soRepublishFolder": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cmake\\debug\\obj\\x86", "abiPlatformVersion": 24, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DANDROID_STL=c++_shared", "-DREACT_NATIVE_MINOR_VERSION=77", "-DANDROID_TOOLCHAIN=clang", "-DREACT_NATIVE_DIR=D:/Projects/EasyCura/frontend/node_modules/react-native", "-DJS_RUNTIME=hermes", "-DJS_RUNTIME_DIR=D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native\\sdks\\hermes", "-DIS_NEW_ARCHITECTURE_ENABLED=true", "-DIS_REANIMATED_EXAMPLE_APP=false", "-DREANIMATED_VERSION=3.18.0", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON", "-DHERMES_ENABLE_DEBUGGER=1"], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": ["worklets", "reanimated"], "implicitBuildTargetSet": ["reanimated", "worklets"], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\.cxx", "intermediatesBaseFolder": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\build\\intermediates", "intermediatesFolder": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx", "gradleModulePathName": ":react-native-reanimated", "moduleRootFolder": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android", "moduleBuildFile": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\build.gradle", "makeFile": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "ndkFolderBeforeSymLinking": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "ndkVersion": "26.1.10909125", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 34, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe", "cmakeVersionFromDsl": "3.22.1"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "x86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\Projects\\EasyCura\\frontend\\android", "sdkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": ["C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar"], "prefabPackages": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\24a24672aa86199034f630fefd9c14b9\\transformed\\jetified-react-android-0.77.2-debug\\prefab", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a84ae05983792e0a15d8128200f58f93\\transformed\\jetified-hermes-android-0.77.2-debug\\prefab", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5e3efb82e6a6d5499bf07e0e92ffee8d\\transformed\\jetified-fbjni-0.7.0\\prefab"], "prefabPackageConfigurations": [], "stlType": "c++_shared", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\f75j4i29\\prefab\\x86", "isActiveAbi": true, "fullConfigurationHash": "f75j4i292c4n2jsa1b1nk317f5v4x674yh4kt5k1fl1s1h2p355x35", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.7.2.\n#   - $NDK is the path to NDK 26.1.10909125.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HD:/Projects/EasyCura/frontend/node_modules/react-native-reanimated/android\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=24\n-DANDROID_PLATFORM=android-24\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-<PERSON><PERSON>KE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:/Projects/EasyCura/frontend/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:/Projects/EasyCura/frontend/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-DCMAKE_FIND_ROOT_PATH=D:/Projects/EasyCura/frontend/node_modules/react-native-reanimated/android/.cxx/Debug/$HASH/prefab/$ABI/prefab\n-BD:/Projects/EasyCura/frontend/node_modules/react-native-reanimated/android/.cxx/Debug/$HASH/$ABI\n-GNinja\n-DANDROID_STL=c++_shared\n-DREACT_NATIVE_MINOR_VERSION=77\n-DANDROID_TOOLCHAIN=clang\n-DREACT_NATIVE_DIR=D:/Projects/EasyCura/frontend/node_modules/react-native\n-DJS_RUNTIME=hermes\n-DJS_RUNTIME_DIR=D:/Projects/EasyCura/frontend/node_modules/react-native/sdks/hermes\n-DIS_NEW_ARCHITECTURE_ENABLED=true\n-DIS_REANIMATED_EXAMPLE_APP=false\n-DREANIMATED_VERSION=3.18.0\n-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\n-DHERMES_ENABLE_DEBUGGER=1", "configurationArguments": ["-HD:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=24", "-DANDROID_PLATFORM=android-24", "-DANDROID_ABI=x86", "-DCMAKE_ANDROID_ARCH_ABI=x86", "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\f75j4i29\\obj\\x86", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\f75j4i29\\obj\\x86", "-DCMAKE_BUILD_TYPE=Debug", "-DCMAKE_FIND_ROOT_PATH=D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\f75j4i29\\prefab\\x86\\prefab", "-BD:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\f75j4i29\\x86", "-<PERSON><PERSON><PERSON><PERSON>", "-DANDROID_STL=c++_shared", "-DREACT_NATIVE_MINOR_VERSION=77", "-DANDROID_TOOLCHAIN=clang", "-DREACT_NATIVE_DIR=D:/Projects/EasyCura/frontend/node_modules/react-native", "-DJS_RUNTIME=hermes", "-DJS_RUNTIME_DIR=D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native\\sdks\\hermes", "-DIS_NEW_ARCHITECTURE_ENABLED=true", "-DIS_REANIMATED_EXAMPLE_APP=false", "-DREANIMATED_VERSION=3.18.0", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON", "-DHERMES_ENABLE_DEBUGGER=1"], "stlLibraryFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "intermediatesParentFolder": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\f75j4i29"}