import React, {useEffect, useState} from 'react';
import {RefreshControl} from 'react-native';
import useLocation from '../../../components/hooks/useLocation';
import {HomePatientPageStyled} from './home-patient.page.styled.ts';
import {useDispatch, useSelector} from 'react-redux';
import {Pages} from '../../../navigation/constants';
import Welcome from '../../../components/shared/welcome/welcome.tsx';
import NewsCard from '../../../components/base/card/news-card/news-card.tsx';
import {
  homepagePatientAction,
  homepagePatientResetState,
  setPatientCurrentPositionAction,
} from '../../../store/patient/home/<USER>';
import CustomCarousel from '../../../components/base/carousel/carousel.tsx';
import MainContainer from '../../../components/base/main-container/main-container.tsx';
import SearchbarCard from '../../../components/shared/search-card/searchbar-card.tsx';
import {navigateToSelectAddressPage} from '../../shared/select-address/constants';
import {useDialog} from '../../../components/base/dialog/hooks/dialog.hook.ts';
import DialogContent from '../../../components/base/dialog/components/dialog-content.tsx';
import {IconType} from '../../../components/base/icon/icon.tsx';
import {strings} from '../../../translation/strings.ts';
import {it} from '../../../translation/supportedlanguage.ts';
import TopProfessions from './components/top-professions/top-professions.tsx';
import TopProfessionals from './components/top-professionals/top-professionals.tsx';
import {useDevice} from '../../../components/base/device-info-context/hooks/device-info-context.hooks.tsx';
import {
  getItemCarouselHeight,
  MIN_HEIGHT_CAROUSEL_ITEM,
} from '../../../components/base/carousel/constants';
import AvailableProfessionals from './components/available-professionals/available-professionals.tsx';

const HomePatientPage = (props: any) => {
  const dispatch = useDispatch();
  const {openDialog, closeDialog} = useDialog();
  const {getFontSize} = useDevice();

  const [itemCarouselHeight, setItemCarouselHeight] = useState(
    MIN_HEIGHT_CAROUSEL_ITEM,
  );

  const {
    permissionGranted,
    requestLocation,
  } = useLocation({
    autoRequest: true,
    enableGeocoding: true,
    onLocationUpdate: (_, address) => {
      if (address) {
        dispatch(setPatientCurrentPositionAction(address));
      }
    },
    onError: (error) => {
      console.error('Location error:', error);
      if (error.includes('permission')) {
        openDialog(
          <DialogContent
            topIcon={IconType.Warning}
            title={strings(it.attention)}
            text={strings(it.errorMissedPermissionsPosition)}
            buttons={[{ onPress: () => closeDialog(), label: 'OK' }]}
          />,
        );
      } else {
        openDialog(
          <DialogContent
            topIcon={IconType.Warning}
            title={strings(it.attention)}
            text={strings(it.homepagePatientPositionNotFound)}
            buttons={[{onPress: () => closeDialog()}]}
          />,
        );
      }
    },
  });

  const {
    loading,
    dataFetched,
    news,
    professionalTypes,
    currentPosition,
    topProfessionals,
    professionalsAvailable,
  } = useSelector((state: any) => {
    return state.homepagePatient;
  });

  const {professionalTypesList} = useSelector((state: any) => {
    return state.professionalTypes;
  });

  const {user} = useSelector((state: any) => {
    return state.user;
  });

  const getNamePosition = (permission: boolean, position: any) => {
    if (permission && position) {
      if (position.formattedAddress) {
        return position.formattedAddress;
      }
      
      if (position.city && position.country) {
        if (position.region) {
          return `${position.city}, ${position.region}, ${position.country}`;
        }
        return `${position.city}, ${position.country}`;
      }
      
      if (position.subregion && position.country) {
        return `${position.subregion}, ${position.country}`;
      }
      
      if (position.latitude && position.longitude) {
        return `${position.latitude.toFixed(4)}, ${position.longitude.toFixed(4)}`;
      }
      
      return strings(it.homepagePatientNotFound);
    } else {
      return strings(it.homepagePatientNotFound);
    }
  };

  const renderItem = ({item}: any, index: number) => {
    return (
      <HomePatientPageStyled.ContainerItemCarousel
        key={index}
        onLayout={(event: any) =>
          getItemCarouselHeight(
            event,
            itemCarouselHeight,
            setItemCarouselHeight,
          )
        }>
        <NewsCard
          title={item.title}
          image={item.img}
          buttonText={item.button_text}
          buttonLink={item.button_link}
        />
      </HomePatientPageStyled.ContainerItemCarousel>
    );
  };

  useEffect(() => {
    dispatch(homepagePatientAction({isLoggedUser: !!user}));
  }, []);

  useEffect(() => {
    if (props.route.params?.position) {
      dispatch(setPatientCurrentPositionAction(props.route.params?.position));
    }
  }, [props.route.params]);

  return (
    <MainContainer loading={loading}>
      <HomePatientPageStyled.ContainerLists
        contentContainerStyle={{flexGrow: 1}}
        refreshControl={
          <RefreshControl
            refreshing={false}
            onRefresh={() => {
              dispatch(homepagePatientResetState());
              dispatch(homepagePatientAction({isLoggedUser: !!user}));
              requestLocation().catch(error => {
                console.error('Location refresh failed:', error);
              });
            }}
          />
        }>
        <HomePatientPageStyled.Container>
          <HomePatientPageStyled.ContainerHeader>
            <Welcome navigation={props.navigation} />
            <HomePatientPageStyled.Break />
            <SearchbarCard
              position={getNamePosition(!!permissionGranted, currentPosition)}
              onPressLocation={() => {
                if (permissionGranted && currentPosition) {
                  navigateToSelectAddressPage(
                    props.navigation,
                    Pages.PatientNavigation,
                    'position',
                    'HomePatient',
                  );
                } else {
                  requestLocation().catch(error => {
                    console.error('Location request failed:', error);
                  });
                }
              }}
              onPressSearch={() => {
                props.navigation.navigate(Pages.PatientNavigation, {
                  params: {},
                  screen: 'Search',
                });
              }}
            />
          </HomePatientPageStyled.ContainerHeader>

          {dataFetched && (
            <HomePatientPageStyled.ContainerContentLists>
              <TopProfessions
                professionalTypes={professionalTypes}
                professionalTypesList={professionalTypesList}
                navigation={props.navigation}
              />
              <TopProfessionals
                topProfessionals={topProfessionals}
                navigation={props.navigation}
              />
              <HomePatientPageStyled.NewsContainer>
                {news.newsList.length > 0 && (
                  <HomePatientPageStyled.ContainerCarousel>
                    <CustomCarousel
                      renderItem={renderItem}
                      arrayData={news.newsList}
                      itemHeight={itemCarouselHeight}
                    />
                  </HomePatientPageStyled.ContainerCarousel>
                )}
                {news.error && (
                  <HomePatientPageStyled.ContainerError
                    getFontSize={getFontSize}>
                    {strings(it.homepagePatientErrorNews)}
                  </HomePatientPageStyled.ContainerError>
                )}
              </HomePatientPageStyled.NewsContainer>
              <AvailableProfessionals
                availableProfessionals={professionalsAvailable}
                navigation={props.navigation}
              />
            </HomePatientPageStyled.ContainerContentLists>
          )}
        </HomePatientPageStyled.Container>
      </HomePatientPageStyled.ContainerLists>
    </MainContainer>
  );
};

export default HomePatientPage;
