import {useState, useEffect, useCallback} from 'react';
import {
  requestLocationAndGetPosition,
  checkLocationPermissions,
  showLocationPermissionDeniedAlert,
  showLocationErrorAlert,
  LocationCoordinates,
} from '../../utils/location-permissions';
import Geocoder from 'react-native-geocoding';
import {getFormattedAddressName} from '../../utils';

export interface AddressData {
  street: string;
  streetNumber: string;
  city: string;
  postalCode: string;
  region: string;
  country: string;
  formattedAddress: string;
  subregion: string;
  latitude: number;
  longitude: number;
}

export interface LocationState {
  coordinates: LocationCoordinates | null;
  addressData: AddressData | null;
  loading: boolean;
  error: string | null;
  permissionGranted: boolean | null;
}

export interface UseLocationOptions {
  autoRequest?: boolean;
  enableGeocoding?: boolean;
  onLocationUpdate?: (coordinates: LocationCoordinates, addressData?: AddressData) => void;
  onError?: (error: string) => void;
}

export const useLocation = (options: UseLocationOptions = {}) => {
  const {
    autoRequest = false,
    enableGeocoding = true,
    onLocationUpdate,
    onError,
  } = options;

  const [state, setState] = useState<LocationState>({
    coordinates: null,
    addressData: null,
    loading: false,
    error: null,
    permissionGranted: null,
  });

  const updateState = useCallback((updates: Partial<LocationState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const geocodeCoordinates = useCallback(async (coordinates: LocationCoordinates): Promise<AddressData | null> => {
    try {
      const response = await Geocoder.from(coordinates.latitude, coordinates.longitude);
      
      if (!response.results || response.results.length === 0) {
        throw new Error('No geocoding results found');
      }

      const addressComponent = response.results[0];
      if (!addressComponent.address_components) {
        throw new Error('Invalid address components');
      }

      const formattedAddress = getFormattedAddressName(addressComponent, false);

      const addressData = {
        street: addressComponent.address_components.find(
          (component: any) => component.types.includes('route')
        )?.long_name || '',
        streetNumber: addressComponent.address_components.find(
          (component: any) => component.types.includes('street_number')
        )?.long_name || '',
        city: addressComponent.address_components.find(
          (component: any) => component.types.includes('locality')
        )?.long_name ||
        addressComponent.address_components.find(
          (component: any) => component.types.includes('administrative_area_level_3')
        )?.long_name || '',
        postalCode: addressComponent.address_components.find(
          (component: any) => component.types.includes('postal_code')
        )?.long_name || '',
        region: addressComponent.address_components.find(
          (component: any) => component.types.includes('administrative_area_level_1')
        )?.long_name || '',
        country: addressComponent.address_components.find(
          (component: any) => component.types.includes('country')
        )?.long_name || '',
        subregion: addressComponent.address_components.find(
          (component: any) => component.types.includes('administrative_area_level_2')
        )?.long_name || '',
        formattedAddress,
        latitude: coordinates.latitude,
        longitude: coordinates.longitude,
      };

      return addressData;
    } catch (error) {
      console.error('Geocoding error:', error);
      
      return {
        street: '',
        streetNumber: '',
        city: '',
        postalCode: '',
        region: '',
        country: '',
        formattedAddress: `${coordinates.latitude.toFixed(4)}, ${coordinates.longitude.toFixed(4)}`,
        subregion: '',
        latitude: coordinates.latitude,
        longitude: coordinates.longitude,
      };
    }
  }, []);

  const requestLocation = useCallback(async () => {
    updateState({ loading: true, error: null });

    try {
      const coordinates = await requestLocationAndGetPosition();
      updateState({ coordinates, permissionGranted: true });

      let addressData: AddressData | null = null;
      
      if (enableGeocoding) {
        addressData = await geocodeCoordinates(coordinates);
        updateState({ addressData });
      }

      updateState({ loading: false });
      
      if (onLocationUpdate) {
        onLocationUpdate(coordinates, addressData || undefined);
      }

      return { coordinates, addressData };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown location error';
      updateState({ 
        loading: false, 
        error: errorMessage,
        permissionGranted: errorMessage.includes('permission') ? false : null
      });

      if (onError) {
        onError(errorMessage);
      } else if (errorMessage.includes('permission')) {
        showLocationPermissionDeniedAlert();
      } else {
        showLocationErrorAlert(errorMessage);
      }

      throw error;
    }
  }, [enableGeocoding, geocodeCoordinates, onLocationUpdate, onError, updateState]);

  const checkPermissions = useCallback(async () => {
    try {
      const granted = await checkLocationPermissions();
      updateState({ permissionGranted: granted });
      return granted;
    } catch (error) {
      console.error('Error checking permissions:', error);
      updateState({ permissionGranted: false });
      return false;
    }
  }, [updateState]);

  const resetLocation = useCallback(() => {
    setState({
      coordinates: null,
      addressData: null,
      loading: false,
      error: null,
      permissionGranted: null,
    });
  }, []);

  useEffect(() => {
    if (autoRequest) {
      requestLocation().catch(error => {
        console.error('Auto location request failed:', error);
      });
    }
  }, [autoRequest, requestLocation]);

  return {
    coordinates: state.coordinates,
    addressData: state.addressData,
    loading: state.loading,
    error: state.error,
    permissionGranted: state.permissionGranted,
    
    requestLocation,
    checkPermissions,
    resetLocation,
    geocodeCoordinates,
    
    hasLocation: !!state.coordinates,
    hasAddress: !!state.addressData,
    isReady: !state.loading && !!state.coordinates,
  };
};

export default useLocation;
