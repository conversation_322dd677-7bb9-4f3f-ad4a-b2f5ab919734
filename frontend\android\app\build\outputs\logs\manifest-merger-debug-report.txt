-- Merging decision tree log ---
manifest
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:1:1-72:12
MERGED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:1:1-72:12
INJECTED from D:\Projects\EasyCura\frontend\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from D:\Projects\EasyCura\frontend\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from D:\Projects\EasyCura\frontend\android\app\src\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-screens] D:\Projects\EasyCura\frontend\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:invertase_react-native-apple-authentication] D:\Projects\EasyCura\frontend\node_modules\@invertase\react-native-apple-authentication\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-8:12
MERGED from [:react-native-community_datetimepicker] D:\Projects\EasyCura\frontend\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-google-signin_google-signin] D:\Projects\EasyCura\frontend\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:stripe_stripe-react-native] D:\Projects\EasyCura\frontend\node_modules\@stripe\stripe-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] D:\Projects\EasyCura\frontend\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-iap] D:\Projects\EasyCura\frontend\node_modules\react-native-iap\android\build\intermediates\merged_manifest\playDebug\processPlayDebugManifest\AndroidManifest.xml:2:1-8:12
MERGED from [:react-native-localize] D:\Projects\EasyCura\frontend\node_modules\react-native-localize\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-maps] D:\Projects\EasyCura\frontend\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] D:\Projects\EasyCura\frontend\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-video] D:\Projects\EasyCura\frontend\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-31:12
MERGED from [:react-native-async-storage_async-storage] D:\Projects\EasyCura\frontend\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_geolocation] D:\Projects\EasyCura\frontend\node_modules\@react-native-community\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_masked-view] D:\Projects\EasyCura\frontend\node_modules\@react-native-community\masked-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-firebase_auth] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-11:12
MERGED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-30:12
MERGED from [:react-native-picker_picker] D:\Projects\EasyCura\frontend\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-16:12
MERGED from [:react-native-config] D:\Projects\EasyCura\frontend\node_modules\react-native-config\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-device-info] D:\Projects\EasyCura\frontend\node_modules\react-native-device-info\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-document-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-fs] D:\Projects\EasyCura\frontend\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-get-random-values] D:\Projects\EasyCura\frontend\node_modules\react-native-get-random-values\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-orientation-locker] D:\Projects\EasyCura\frontend\node_modules\react-native-orientation-locker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-push-notification] D:\Projects\EasyCura\frontend\node_modules\react-native-push-notification\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] D:\Projects\EasyCura\frontend\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-svg] D:\Projects\EasyCura\frontend\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-ui-lib] D:\Projects\EasyCura\frontend\node_modules\react-native-ui-lib\lib\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.77.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a24672aa86199034f630fefd9c14b9\transformed\jetified-react-android-0.77.2-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.stripe:stripe-android:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e4576c580ac308621bbd06b28c3ea80\transformed\jetified-stripe-android-20.52.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:2:1-77:12
MERGED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:2:1-45:12
MERGED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:2:1-40:12
MERGED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9cce331ffb64663587db3c3239c1f\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:2:1-14:12
MERGED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:2:1-85:12
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61660682525456ea1e61825c06c8a332\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5998a458961ad910e568c20e86b1bee\transformed\jetified-accompanist-themeadapter-appcompat-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\199cbab47ce134fd6afc474eb7d57f60\transformed\jetified-accompanist-themeadapter-material-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95170855ea9029710141d3a5c3e6769c\transformed\jetified-accompanist-themeadapter-material3-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\988a4b26920c4f51e578812e01990de4\transformed\jetified-accompanist-themeadapter-core-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8757ead728de1ee7baf1f340dd153c1e\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52c8b1235080f34adfa9f8a8d6897eed\transformed\jetified-android-maps-utils-3.10.0\AndroidManifest.xml:17:1-28:12
MERGED from [com.stripe:payments-model:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2579b3b7091aa4553d4958d9ca1265e5\transformed\jetified-payments-model-20.52.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f975393435571acd3ca3e67e4333c8b5\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e4df28e397d10a576127de73803f214\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.stripe:hcaptcha:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf73c2ae76c46cb615c2f3af8a4ee83f\transformed\jetified-hcaptcha-20.52.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3084d87a53dce853fa97754a06a4f69\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:stripe-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19a78c8a9b08a447e3ab0002f043dec3\transformed\jetified-stripe-ui-core-20.52.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.android.billingclient:billing-ktx:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb6c4937400eeb2b198ba0535d429527\transformed\jetified-billing-ktx-7.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:2:1-35:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c3cb5b10926108bdf749106ee621f95\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\170cef79e981c7b2e5675ad725000b95\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\355b5614adf8178401d6322cd2a6552b\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\560739c06b6fe948d715bf253fd24fe1\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f970840dd7f6e164a1a50fedd57f47e5\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b137036b4a243a76eb4ef6b4584cf336\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fadf115855abd742ea5db1be34a1842d\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62de3d3dfecf16708474281d8c8790b4\transformed\jetified-glide-4.12.0\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1dcc3235b6bf6a47208327162bfc1af\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f6797e59861223c966649f7ed34da8a\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13c4386b5b058dd4f59989cbb3750e6f\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-wallet:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b96d6f7a71040dfb8db1038442abdb7b\transformed\jetified-play-services-wallet-19.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:16:1-35:12
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbd1c17e9de1d261d00a72c31c9514aa\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c9ea9c0a9a0ded423a00c1c131a5623\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4248941b6b79ca02a22f68de0ad2bacc\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90c414f0d8c5c7d6015150fe17e102e3\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb9aa9e50af4d70673b75654e59eff9\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0aca4c1084c580fd84a1e342864f2bf\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fdc9af17617ef80636447a89d6325740\transformed\jetified-firebase-analytics-22.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba40499d6ccf14a06a3631fd52bf5391\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1a6287ec5705adcba52186458007b35\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50d26564deea5dfafdf74f06065c2972\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31b18409de7e904121375cfc5399a81d\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b6399342f734411a3445f72c00e8999\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\953341a889a6c2f329f0224271e451e0\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16352038dd7ddd4de2755c94323c3f05\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:17:1-145:12
MERGED from [com.stripe:stripe-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\370c2381c81cfed6def64118b06b2997\transformed\jetified-stripe-core-20.52.3\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d39033b31561640c1e1d3121b9e67\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f70a33040068819bda923130b93dbc18\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9c237de43d5bce9c7ecf60dcf3c21\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f247db19ff855ff6e98ee6f6ad58715e\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f9e75e8f31982799eee1bae3519949\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b5a764456fd8071de2cdc2eb84c1302\transformed\jetified-media3-datasource-okhttp-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bac6d70c42747549558ede8d99bc4334\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51adfdc250a6f116b4f051822f5c25f8\transformed\jetified-media3-session-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e83ce3221c0c7c5a3feb3b27f91d7401\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b29f4906af46fd344d9df18e98f3f2\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be9b43fd18ac6fe53e57e445d6671893\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fd365467faececf08b25cb2c7bc1183\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f4ea053c4cc42a4544f4482c4199d0\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a4e8182d7266e6c1b039c0df74009b1\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4562c06eef1aefc5d6dc0a56edfd31a0\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ce7143c4f2df64088b42db95074c386\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dc4b43688aab3000a6413dc37f6950d\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0d50fcce07af1ead5742ece71d1888\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21fd92cd239b05299148ceea32100a41\transformed\webkit-1.14.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51fe0542825ecabc89959ed91fa2eb7b\transformed\jetified-material3-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afa70d7ce39e346b74ce167886d74b32\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4930c0bd9f0840ca3f2aec55fe66b3ba\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c4b2ee7d9df4026106e59aeaf53665\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-flowlayout:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e848cd9734c959e36fb6604681b34bf\transformed\jetified-accompanist-flowlayout-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c340e75db3c7f7d09682c74e0f91e20c\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\597f6b2804024b460781a93fcf31f95f\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8de82ca9d62301b610bf983e372b8908\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05f3e78f103758134ece8fc3491b1fe1\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19d7631cd25f8528b7593e9c691375e\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4edd762e739d5bc65ce105b3810c5de6\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e20db831a6bac46b665344bf988a429\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9509a6a1252c7d12bbc8ae5935d9c1a3\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df862a9921d17c0ba625a11f4b40c114\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6edcaf306b7b0bced34909e92576c6cf\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-viewbinding:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c7ac3833900471d4d21486909525351\transformed\jetified-ui-viewbinding-1.5.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32deb346123320764111514859f928c9\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [io.sentry:sentry-android:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b28462348b8dcc29b68c5dfaf079ac8\transformed\jetified-sentry-android-7.22.5\AndroidManifest.xml:2:1-10:12
MERGED from [io.sentry:sentry-android-ndk:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\899c67490b49a33163f75fb2d72293e0\transformed\jetified-sentry-android-ndk-7.22.5\AndroidManifest.xml:2:1-7:12
MERGED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:2:1-23:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d71b6bc6636049cfa91710404141c520\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\127c8997e725075c8cf5027c0364411a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddc7eee06e745d0473de67fa65b3e624\transformed\jetified-play-services-measurement-sdk-22.0.2\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35ec57673ddb3c10dc99eb500fc832d6\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf9f47a57c2bd3aeac1de24bdec1db6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5369eeab582eb175a9b38824535717c\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82a3c25d40028c313a02ab0084b17eaf\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b83abd0a9e7e879a02141c74a773cac\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf9bf17b0072a132048e25f5106dae1b\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b664caaab4e5d4976de1b02959dcfec\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25b7098eba539877dc42cbc749a2fd5d\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d4e59964faa5313026d1d45728924a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0776b91425488ac8dce6808c5c5afbb\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c91034905ca39efe69a659a8881112d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c2dcae86878172fff40a4ddb686c11f\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82c86372c359925109c1930e4339d10c\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92d4bd8399e6042d15ccd64b994d2bb7\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f75e2f5027557a76790a63619407dae\transformed\jetified-runtime-livedata-1.5.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5cc81aad252b6789c136987d5fcc9afd\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b96e6857e29018a0c6f00798a7b9527\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1537d00f1b5f2aaaaaac3c5273cbb71a\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8be9d2d36d9839991de1c4e2f54c1b\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85bae30ed434eb28baef6b00d446b2ed\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4da8075ef694eaadf8347e2b12eeb10f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db842c481bbb035cc265114857c3c788\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6579377b27e61a245a3a7ddc2dd463c0\transformed\browser-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18a1404a737ab3a298b99291a478e140\transformed\jetified-fresco-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0397f2bc77d60935dd99b4569cb9a30\transformed\jetified-imagepipeline-okhttp3-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e9ef0be0c745c814fe2609cca78a464\transformed\jetified-drawee-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc0d2e53812fffe2667b8800ca004098\transformed\jetified-nativeimagefilters-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\086f94370da8231ed853773680d29815\transformed\jetified-memory-type-native-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b9a7e377997e430a61a4310adb5128f\transformed\jetified-memory-type-java-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\330e3da485a776b2f05e460664418c05\transformed\jetified-imagepipeline-native-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cce25d7c66728d63ac33a18c542ab57\transformed\jetified-memory-type-ashmem-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28ccb09a016b41ca77c5763dbd2922b4\transformed\jetified-imagepipeline-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4832e9dbf7628a73d4014617e3bad2ad\transformed\jetified-nativeimagetranscoder-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf1fcc162134d806881bfe348e62b6f4\transformed\jetified-imagepipeline-base-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed5ff9cd8c2cd3bf0a9067a73ac73903\transformed\jetified-urimod-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aaa522d0ab367a168809ecd9d90b144\transformed\jetified-vito-source-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1f8024089fd6fea3a635a33c9f80c46\transformed\jetified-middleware-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d091dd8b81a90400a1492fe6ef6b845e\transformed\jetified-ui-common-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\132669564a13df33aedfc9003f1b23b0\transformed\jetified-soloader-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f5fd51b96bc8f1b6d54d4b2591c601c\transformed\jetified-fbcore-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2d669ee9caeba2a508b7ca6e7fc4e1b\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7f76f78c978e674a1336968dc051831\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed10a01b69c034e4c815313926b71830\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c1148e615e12339891741fb52f575da\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb7838a6391355fcaf95d7a87d1ab860\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad20a6d6d8b2027aa19b78125ef41da\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17a8ba29fd77033619df6eec30e346f5\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abf2184437801464e9b88dea2c2d0764\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\feab3837c1711f87bcf2772443a3e944\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c81ed56bb7cc3530b8a9d137342d0414\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd6d437a67db0f3b827c540bf0a1bbcd\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22e8e4273a79efbd453bda6c4ecb77c9\transformed\jetified-room-ktx-2.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58057c61ab78d9bdf8d473ce6e8d5c2e\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1df0ef958be93b2f6a07daeefeadec68\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b6b7c5fcece6be727a91b3529c36c17\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b3192b9ba86b0cc71684aa361909b45\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b54621a91cc90f8c923b66f6b8e760ab\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ac6e233e752b126ef60b054dc6868aa\transformed\jetified-play-services-measurement-base-22.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3184320098644e8a0b31ce89621b4fc8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfcf7b83cc9daae29a723dcdc9831e0b\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6676c797b507573c5383e5d2dd1fe087\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d2108b98508c4ee55e20557db7a7233\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df95c34d4174f67f900e212e9ebc6f7f\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea3f756f26d5ac40fd2f114f988d5982\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dc46601abed3bc5f006f747cfcd35d8\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\669e178a604925a8e79c04477f68672a\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84ed9d443529fc901d30d5f764ef1143\transformed\jetified-ui-core-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d8283ef7ea45a9cfcb87061c28904e\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.sentry:sentry-android-replay:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\929ecc092120e7d0f027953e1f728924\transformed\jetified-sentry-android-replay-7.22.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d72cf12e5c658a0cbb03db79daed6a5\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.77.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a84ae05983792e0a15d8128200f58f93\transformed\jetified-hermes-android-0.77.2-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10e7939f59a050be744a3e1c3aa365bd\transformed\jetified-viewbinding-8.7.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a032288e50448a28ff486b801527711d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ae34c9f6b2fdbf5353d97222c9d76fe\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cf8925ed9e70192a1148a4203062da3\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c16c7682157e8b13b48c5d11ce9345b9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d064d13c0c8ffce8f37749721547f1d5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [:react-native-image-resizer] D:\Projects\EasyCura\frontend\node_modules\react-native-image-resizer\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17db74a5889927a25444fece673073a\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b458a5f379eaf7c5df7fade24d336f17\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abbc9c196435969e40e7321b3a078e5e\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afba68633ab233904518bfa16556311f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df1596ce48052a30ec6545621db4794c\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51c6bc2011c8a2b666f74883e5b7bb7f\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0223835a7c285e66b46bffb08c7d6f\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ed255acdc3028ccafc15b3aea8550a\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f91ceaa7cbc96fdd1a6169d0cdf01b8\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\718d55ff7c39f613e7d97f9c231924d3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4dde0f6fb527e120b78758b336480cd\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ff1ace1eea3ad38bda3a3a2fff12cb6\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b985f2eda2053ae8e2f5db6fe8cc1570\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a3ec980e60ad6848d5369074a15b3da\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:2:1-13:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:2:1-52:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e3efb82e6a6d5499bf07e0e92ffee8d\transformed\jetified-fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b198ecf16b59ae0079af471be112cc6b\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9fa070465237736c228043f51912a60\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:2:1-15:12
	package
		INJECTED from D:\Projects\EasyCura\frontend\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Projects\EasyCura\frontend\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:2:11-57
	android:versionCode
		INJECTED from D:\Projects\EasyCura\frontend\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:4:5-66
MERGED from [:react-native-firebase_auth] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:react-native-firebase_auth] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:7:5-67
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:21:5-66
MERGED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:21:5-66
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50d26564deea5dfafdf74f06065c2972\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50d26564deea5dfafdf74f06065c2972\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\953341a889a6c2f329f0224271e451e0\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\953341a889a6c2f329f0224271e451e0\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:7:5-67
MERGED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:4:22-64
uses-permission#android.permission.VIBRATE
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:5:5-65
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:5:22-63
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:6:5-80
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:6:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:7:5-78
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:8:5-80
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:9:5-84
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:9:22-82
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:10:5-78
MERGED from [:react-native-firebase_auth] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:react-native-firebase_auth] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
MERGED from [:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:20:5-78
MERGED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:20:5-78
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50d26564deea5dfafdf74f06065c2972\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50d26564deea5dfafdf74f06065c2972\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\953341a889a6c2f329f0224271e451e0\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\953341a889a6c2f329f0224271e451e0\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:24:5-79
MERGED from [com.stripe:stripe-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\370c2381c81cfed6def64118b06b2997\transformed\jetified-stripe-core-20.52.3\AndroidManifest.xml:7:5-79
MERGED from [com.stripe:stripe-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\370c2381c81cfed6def64118b06b2997\transformed\jetified-stripe-core-20.52.3\AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a4e8182d7266e6c1b039c0df74009b1\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a4e8182d7266e6c1b039c0df74009b1\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0d50fcce07af1ead5742ece71d1888\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0d50fcce07af1ead5742ece71d1888\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:10:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:11:5-75
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:11:22-73
uses-permission#android.permission.CAMERA
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:12:5-64
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:12:22-62
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:13:5-80
MERGED from [:react-native-fs] D:\Projects\EasyCura\frontend\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:react-native-fs] D:\Projects\EasyCura\frontend\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:13:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:14:5-79
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:14:22-77
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:15:5-76
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:15:22-74
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:16:5-79
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:16:22-77
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:17:5-70
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:17:22-68
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:18:5-77
MERGED from [com.facebook.react:react-android:0.77.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a24672aa86199034f630fefd9c14b9\transformed\jetified-react-android-0.77.2-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.77.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a24672aa86199034f630fefd9c14b9\transformed\jetified-react-android-0.77.2-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:18:22-75
uses-permission#com.android.vending.BILLING
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:19:5-66
MERGED from [com.android.billingclient:billing-ktx:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb6c4937400eeb2b198ba0535d429527\transformed\jetified-billing-ktx-7.0.0\AndroidManifest.xml:9:5-67
MERGED from [com.android.billingclient:billing-ktx:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb6c4937400eeb2b198ba0535d429527\transformed\jetified-billing-ktx-7.0.0\AndroidManifest.xml:9:5-67
MERGED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:10:5-67
MERGED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:10:5-67
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:19:22-64
application
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:21:5-71:19
MERGED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:21:5-71:19
MERGED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:21:5-71:19
INJECTED from D:\Projects\EasyCura\frontend\android\app\src\debug\AndroidManifest.xml:5:5-8:50
MERGED from [:react-native-community_datetimepicker] D:\Projects\EasyCura\frontend\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-20
MERGED from [:react-native-community_datetimepicker] D:\Projects\EasyCura\frontend\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-20
MERGED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-29:19
MERGED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-29:19
MERGED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-28:19
MERGED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-28:19
MERGED from [:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-14:19
MERGED from [:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-14:19
MERGED from [:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [com.facebook.react:react-android:0.77.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a24672aa86199034f630fefd9c14b9\transformed\jetified-react-android-0.77.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.77.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a24672aa86199034f630fefd9c14b9\transformed\jetified-react-android-0.77.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:20:5-75:19
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:20:5-75:19
MERGED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:7:5-43:19
MERGED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:7:5-43:19
MERGED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:7:5-38:19
MERGED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:7:5-38:19
MERGED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9cce331ffb64663587db3c3239c1f\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9cce331ffb64663587db3c3239c1f\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:14:5-83:19
MERGED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:14:5-83:19
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61660682525456ea1e61825c06c8a332\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61660682525456ea1e61825c06c8a332\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8757ead728de1ee7baf1f340dd153c1e\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8757ead728de1ee7baf1f340dd153c1e\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52c8b1235080f34adfa9f8a8d6897eed\transformed\jetified-android-maps-utils-3.10.0\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52c8b1235080f34adfa9f8a8d6897eed\transformed\jetified-android-maps-utils-3.10.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e4df28e397d10a576127de73803f214\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e4df28e397d10a576127de73803f214\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:18:5-33:19
MERGED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:18:5-33:19
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:28:5-73:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62de3d3dfecf16708474281d8c8790b4\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62de3d3dfecf16708474281d8c8790b4\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f6797e59861223c966649f7ed34da8a\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f6797e59861223c966649f7ed34da8a\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:31:5-34:19
MERGED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:31:5-34:19
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4248941b6b79ca02a22f68de0ad2bacc\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4248941b6b79ca02a22f68de0ad2bacc\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90c414f0d8c5c7d6015150fe17e102e3\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90c414f0d8c5c7d6015150fe17e102e3\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb9aa9e50af4d70673b75654e59eff9\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb9aa9e50af4d70673b75654e59eff9\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0aca4c1084c580fd84a1e342864f2bf\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0aca4c1084c580fd84a1e342864f2bf\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fdc9af17617ef80636447a89d6325740\transformed\jetified-firebase-analytics-22.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fdc9af17617ef80636447a89d6325740\transformed\jetified-firebase-analytics-22.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba40499d6ccf14a06a3631fd52bf5391\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba40499d6ccf14a06a3631fd52bf5391\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1a6287ec5705adcba52186458007b35\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1a6287ec5705adcba52186458007b35\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31b18409de7e904121375cfc5399a81d\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31b18409de7e904121375cfc5399a81d\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b6399342f734411a3445f72c00e8999\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b6399342f734411a3445f72c00e8999\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f9e75e8f31982799eee1bae3519949\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f9e75e8f31982799eee1bae3519949\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:9:5-21:19
MERGED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:9:5-21:19
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddc7eee06e745d0473de67fa65b3e624\transformed\jetified-play-services-measurement-sdk-22.0.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddc7eee06e745d0473de67fa65b3e624\transformed\jetified-play-services-measurement-sdk-22.0.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35ec57673ddb3c10dc99eb500fc832d6\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35ec57673ddb3c10dc99eb500fc832d6\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c91034905ca39efe69a659a8881112d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c91034905ca39efe69a659a8881112d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1df0ef958be93b2f6a07daeefeadec68\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1df0ef958be93b2f6a07daeefeadec68\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b6b7c5fcece6be727a91b3529c36c17\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b6b7c5fcece6be727a91b3529c36c17\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b3192b9ba86b0cc71684aa361909b45\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b3192b9ba86b0cc71684aa361909b45\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b54621a91cc90f8c923b66f6b8e760ab\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b54621a91cc90f8c923b66f6b8e760ab\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ac6e233e752b126ef60b054dc6868aa\transformed\jetified-play-services-measurement-base-22.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ac6e233e752b126ef60b054dc6868aa\transformed\jetified-play-services-measurement-base-22.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3184320098644e8a0b31ce89621b4fc8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3184320098644e8a0b31ce89621b4fc8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dc46601abed3bc5f006f747cfcd35d8\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dc46601abed3bc5f006f747cfcd35d8\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cf8925ed9e70192a1148a4203062da3\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cf8925ed9e70192a1148a4203062da3\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d064d13c0c8ffce8f37749721547f1d5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d064d13c0c8ffce8f37749721547f1d5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afba68633ab233904518bfa16556311f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afba68633ab233904518bfa16556311f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\718d55ff7c39f613e7d97f9c231924d3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\718d55ff7c39f613e7d97f9c231924d3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a3ec980e60ad6848d5369074a15b3da\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a3ec980e60ad6848d5369074a15b3da\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:11:5-20
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b198ecf16b59ae0079af471be112cc6b\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b198ecf16b59ae0079af471be112cc6b\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9fa070465237736c228043f51912a60\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:9:5-13:19
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9fa070465237736c228043f51912a60\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:9:5-13:19
	android:extractNativeLibs
		INJECTED from D:\Projects\EasyCura\frontend\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:30:9-35
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:30:9-35
	android:label
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:23:9-41
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:23:9-41
	tools:ignore
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\debug\AndroidManifest.xml:8:9-48
	android:roundIcon
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:25:9-54
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:25:9-54
	tools:targetApi
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:29:9-29
	android:icon
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:24:9-43
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:24:9-43
	android:allowBackup
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:26:9-36
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:26:9-36
	android:theme
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:27:9-40
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:27:9-40
	android:usesCleartextTraffic
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:28:9-44
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:22:9-40
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:22:9-40
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:32:9-85
MERGED from [com.facebook.react:react-android:0.77.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a24672aa86199034f630fefd9c14b9\transformed\jetified-react-android-0.77.2-debug\AndroidManifest.xml:19:9-21:40
MERGED from [com.facebook.react:react-android:0.77.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a24672aa86199034f630fefd9c14b9\transformed\jetified-react-android-0.77.2-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.77.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a24672aa86199034f630fefd9c14b9\transformed\jetified-react-android-0.77.2-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:32:19-83
activity#com.easycura.MainActivity
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:34:9-50:20
	android:label
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:36:13-45
	android:launchMode
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:41:13-44
	android:windowSoftInputMode
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:42:13-55
	android:exported
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:45:13-36
	android:roundIcon
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:38:13-58
	android:configChanges
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:40:13-122
	android:icon
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:37:13-47
	android:allowBackup
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:39:13-40
	android:theme
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:43:13-44
	android:usesCleartextTraffic
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:44:13-48
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:35:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:46:13-49:29
action#android.intent.action.MAIN
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:47:17-68
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:47:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:48:17-76
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:48:27-74
meta-data#com.dieam.reactnativepushnotification.notification_foreground
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:52:9-120
	android:value
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:52:97-118
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:52:20-96
meta-data#com.dieam.reactnativepushnotification.notification_color
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:53:9-125
	android:resource
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:53:92-123
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:53:20-91
meta-data#com.google.android.geo.API_KEY
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:54:9-123
	android:value
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:54:66-121
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:54:20-65
receiver#com.dieam.reactnativepushnotification.modules.RNPushNotificationActions
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:56:9-132
	android:exported
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:56:106-130
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:56:19-105
receiver#com.dieam.reactnativepushnotification.modules.RNPushNotificationPublisher
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:57:9-109
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:57:19-107
receiver#com.dieam.reactnativepushnotification.modules.RNPushNotificationBootEventReceiver
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:58:9-64:20
	android:exported
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:58:116-140
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:58:19-115
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:com.htc.intent.action.QUICKBOOT_POWERON
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:59:13-63:29
action#android.intent.action.BOOT_COMPLETED
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:60:17-78
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:60:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:61:17-81
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:61:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:62:17-81
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:62:25-79
service#com.dieam.reactnativepushnotification.modules.RNPushNotificationListenerService
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:66:9-70:19
	android:exported
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:66:113-137
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:66:18-112
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:67:13-69:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:68:17-77
	android:name
		ADDED from D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:68:25-75
uses-sdk
INJECTED from D:\Projects\EasyCura\frontend\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Projects\EasyCura\frontend\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\Projects\EasyCura\frontend\android\app\src\debug\AndroidManifest.xml
MERGED from [:react-native-screens] D:\Projects\EasyCura\frontend\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\Projects\EasyCura\frontend\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:invertase_react-native-apple-authentication] D:\Projects\EasyCura\frontend\node_modules\@invertase\react-native-apple-authentication\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:invertase_react-native-apple-authentication] D:\Projects\EasyCura\frontend\node_modules\@invertase\react-native-apple-authentication\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-community_datetimepicker] D:\Projects\EasyCura\frontend\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_datetimepicker] D:\Projects\EasyCura\frontend\node_modules\@react-native-community\datetimepicker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-google-signin_google-signin] D:\Projects\EasyCura\frontend\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-google-signin_google-signin] D:\Projects\EasyCura\frontend\node_modules\@react-native-google-signin\google-signin\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:stripe_stripe-react-native] D:\Projects\EasyCura\frontend\node_modules\@stripe\stripe-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:stripe_stripe-react-native] D:\Projects\EasyCura\frontend\node_modules\@stripe\stripe-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] D:\Projects\EasyCura\frontend\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] D:\Projects\EasyCura\frontend\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-iap] D:\Projects\EasyCura\frontend\node_modules\react-native-iap\android\build\intermediates\merged_manifest\playDebug\processPlayDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-iap] D:\Projects\EasyCura\frontend\node_modules\react-native-iap\android\build\intermediates\merged_manifest\playDebug\processPlayDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-localize] D:\Projects\EasyCura\frontend\node_modules\react-native-localize\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-localize] D:\Projects\EasyCura\frontend\node_modules\react-native-localize\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] D:\Projects\EasyCura\frontend\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] D:\Projects\EasyCura\frontend\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\Projects\EasyCura\frontend\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\Projects\EasyCura\frontend\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-video] D:\Projects\EasyCura\frontend\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-video] D:\Projects\EasyCura\frontend\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] D:\Projects\EasyCura\frontend\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] D:\Projects\EasyCura\frontend\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_geolocation] D:\Projects\EasyCura\frontend\node_modules\@react-native-community\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_geolocation] D:\Projects\EasyCura\frontend\node_modules\@react-native-community\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_masked-view] D:\Projects\EasyCura\frontend\node_modules\@react-native-community\masked-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_masked-view] D:\Projects\EasyCura\frontend\node_modules\@react-native-community\masked-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_auth] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_auth] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-picker_picker] D:\Projects\EasyCura\frontend\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] D:\Projects\EasyCura\frontend\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-config] D:\Projects\EasyCura\frontend\node_modules\react-native-config\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-config] D:\Projects\EasyCura\frontend\node_modules\react-native-config\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-device-info] D:\Projects\EasyCura\frontend\node_modules\react-native-device-info\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-device-info] D:\Projects\EasyCura\frontend\node_modules\react-native-device-info\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-document-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-document-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fs] D:\Projects\EasyCura\frontend\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fs] D:\Projects\EasyCura\frontend\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-get-random-values] D:\Projects\EasyCura\frontend\node_modules\react-native-get-random-values\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-get-random-values] D:\Projects\EasyCura\frontend\node_modules\react-native-get-random-values\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-orientation-locker] D:\Projects\EasyCura\frontend\node_modules\react-native-orientation-locker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-orientation-locker] D:\Projects\EasyCura\frontend\node_modules\react-native-orientation-locker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-push-notification] D:\Projects\EasyCura\frontend\node_modules\react-native-push-notification\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-push-notification] D:\Projects\EasyCura\frontend\node_modules\react-native-push-notification\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] D:\Projects\EasyCura\frontend\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] D:\Projects\EasyCura\frontend\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] D:\Projects\EasyCura\frontend\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] D:\Projects\EasyCura\frontend\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-ui-lib] D:\Projects\EasyCura\frontend\node_modules\react-native-ui-lib\lib\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-ui-lib] D:\Projects\EasyCura\frontend\node_modules\react-native-ui-lib\lib\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.77.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a24672aa86199034f630fefd9c14b9\transformed\jetified-react-android-0.77.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.77.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a24672aa86199034f630fefd9c14b9\transformed\jetified-react-android-0.77.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.stripe:stripe-android:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e4576c580ac308621bbd06b28c3ea80\transformed\jetified-stripe-android-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-android:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e4576c580ac308621bbd06b28c3ea80\transformed\jetified-stripe-android-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9cce331ffb64663587db3c3239c1f\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9cce331ffb64663587db3c3239c1f\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61660682525456ea1e61825c06c8a332\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61660682525456ea1e61825c06c8a332\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5998a458961ad910e568c20e86b1bee\transformed\jetified-accompanist-themeadapter-appcompat-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e5998a458961ad910e568c20e86b1bee\transformed\jetified-accompanist-themeadapter-appcompat-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\199cbab47ce134fd6afc474eb7d57f60\transformed\jetified-accompanist-themeadapter-material-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\199cbab47ce134fd6afc474eb7d57f60\transformed\jetified-accompanist-themeadapter-material-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95170855ea9029710141d3a5c3e6769c\transformed\jetified-accompanist-themeadapter-material3-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95170855ea9029710141d3a5c3e6769c\transformed\jetified-accompanist-themeadapter-material3-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\988a4b26920c4f51e578812e01990de4\transformed\jetified-accompanist-themeadapter-core-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\988a4b26920c4f51e578812e01990de4\transformed\jetified-accompanist-themeadapter-core-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8757ead728de1ee7baf1f340dd153c1e\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8757ead728de1ee7baf1f340dd153c1e\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52c8b1235080f34adfa9f8a8d6897eed\transformed\jetified-android-maps-utils-3.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52c8b1235080f34adfa9f8a8d6897eed\transformed\jetified-android-maps-utils-3.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.stripe:payments-model:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2579b3b7091aa4553d4958d9ca1265e5\transformed\jetified-payments-model-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-model:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2579b3b7091aa4553d4958d9ca1265e5\transformed\jetified-payments-model-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f975393435571acd3ca3e67e4333c8b5\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f975393435571acd3ca3e67e4333c8b5\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e4df28e397d10a576127de73803f214\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e4df28e397d10a576127de73803f214\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.stripe:hcaptcha:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf73c2ae76c46cb615c2f3af8a4ee83f\transformed\jetified-hcaptcha-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:hcaptcha:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf73c2ae76c46cb615c2f3af8a4ee83f\transformed\jetified-hcaptcha-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3084d87a53dce853fa97754a06a4f69\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3084d87a53dce853fa97754a06a4f69\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19a78c8a9b08a447e3ab0002f043dec3\transformed\jetified-stripe-ui-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19a78c8a9b08a447e3ab0002f043dec3\transformed\jetified-stripe-ui-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.android.billingclient:billing-ktx:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb6c4937400eeb2b198ba0535d429527\transformed\jetified-billing-ktx-7.0.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.billingclient:billing-ktx:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb6c4937400eeb2b198ba0535d429527\transformed\jetified-billing-ktx-7.0.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c3cb5b10926108bdf749106ee621f95\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c3cb5b10926108bdf749106ee621f95\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\170cef79e981c7b2e5675ad725000b95\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\170cef79e981c7b2e5675ad725000b95\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\355b5614adf8178401d6322cd2a6552b\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\355b5614adf8178401d6322cd2a6552b\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\560739c06b6fe948d715bf253fd24fe1\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\560739c06b6fe948d715bf253fd24fe1\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f970840dd7f6e164a1a50fedd57f47e5\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f970840dd7f6e164a1a50fedd57f47e5\transformed\jetified-navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b137036b4a243a76eb4ef6b4584cf336\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b137036b4a243a76eb4ef6b4584cf336\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:21:5-23:151
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fadf115855abd742ea5db1be34a1842d\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fadf115855abd742ea5db1be34a1842d\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62de3d3dfecf16708474281d8c8790b4\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62de3d3dfecf16708474281d8c8790b4\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1dcc3235b6bf6a47208327162bfc1af\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1dcc3235b6bf6a47208327162bfc1af\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f6797e59861223c966649f7ed34da8a\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0f6797e59861223c966649f7ed34da8a\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13c4386b5b058dd4f59989cbb3750e6f\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13c4386b5b058dd4f59989cbb3750e6f\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-wallet:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b96d6f7a71040dfb8db1038442abdb7b\transformed\jetified-play-services-wallet-19.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:19.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b96d6f7a71040dfb8db1038442abdb7b\transformed\jetified-play-services-wallet-19.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:26:5-43
MERGED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:26:5-43
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbd1c17e9de1d261d00a72c31c9514aa\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbd1c17e9de1d261d00a72c31c9514aa\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c9ea9c0a9a0ded423a00c1c131a5623\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c9ea9c0a9a0ded423a00c1c131a5623\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4248941b6b79ca02a22f68de0ad2bacc\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4248941b6b79ca02a22f68de0ad2bacc\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90c414f0d8c5c7d6015150fe17e102e3\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90c414f0d8c5c7d6015150fe17e102e3\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb9aa9e50af4d70673b75654e59eff9\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-identity:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb9aa9e50af4d70673b75654e59eff9\transformed\jetified-play-services-identity-18.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0aca4c1084c580fd84a1e342864f2bf\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0aca4c1084c580fd84a1e342864f2bf\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fdc9af17617ef80636447a89d6325740\transformed\jetified-firebase-analytics-22.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fdc9af17617ef80636447a89d6325740\transformed\jetified-firebase-analytics-22.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba40499d6ccf14a06a3631fd52bf5391\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba40499d6ccf14a06a3631fd52bf5391\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1a6287ec5705adcba52186458007b35\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1a6287ec5705adcba52186458007b35\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50d26564deea5dfafdf74f06065c2972\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50d26564deea5dfafdf74f06065c2972\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31b18409de7e904121375cfc5399a81d\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31b18409de7e904121375cfc5399a81d\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b6399342f734411a3445f72c00e8999\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b6399342f734411a3445f72c00e8999\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\953341a889a6c2f329f0224271e451e0\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\953341a889a6c2f329f0224271e451e0\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16352038dd7ddd4de2755c94323c3f05\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16352038dd7ddd4de2755c94323c3f05\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [com.stripe:stripe-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\370c2381c81cfed6def64118b06b2997\transformed\jetified-stripe-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\370c2381c81cfed6def64118b06b2997\transformed\jetified-stripe-core-20.52.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d39033b31561640c1e1d3121b9e67\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d39033b31561640c1e1d3121b9e67\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f70a33040068819bda923130b93dbc18\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f70a33040068819bda923130b93dbc18\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9c237de43d5bce9c7ecf60dcf3c21\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9c237de43d5bce9c7ecf60dcf3c21\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f247db19ff855ff6e98ee6f6ad58715e\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f247db19ff855ff6e98ee6f6ad58715e\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f9e75e8f31982799eee1bae3519949\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f9e75e8f31982799eee1bae3519949\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b5a764456fd8071de2cdc2eb84c1302\transformed\jetified-media3-datasource-okhttp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b5a764456fd8071de2cdc2eb84c1302\transformed\jetified-media3-datasource-okhttp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bac6d70c42747549558ede8d99bc4334\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bac6d70c42747549558ede8d99bc4334\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51adfdc250a6f116b4f051822f5c25f8\transformed\jetified-media3-session-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51adfdc250a6f116b4f051822f5c25f8\transformed\jetified-media3-session-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e83ce3221c0c7c5a3feb3b27f91d7401\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e83ce3221c0c7c5a3feb3b27f91d7401\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b29f4906af46fd344d9df18e98f3f2\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80b29f4906af46fd344d9df18e98f3f2\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be9b43fd18ac6fe53e57e445d6671893\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be9b43fd18ac6fe53e57e445d6671893\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fd365467faececf08b25cb2c7bc1183\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fd365467faececf08b25cb2c7bc1183\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f4ea053c4cc42a4544f4482c4199d0\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55f4ea053c4cc42a4544f4482c4199d0\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a4e8182d7266e6c1b039c0df74009b1\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a4e8182d7266e6c1b039c0df74009b1\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4562c06eef1aefc5d6dc0a56edfd31a0\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4562c06eef1aefc5d6dc0a56edfd31a0\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ce7143c4f2df64088b42db95074c386\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ce7143c4f2df64088b42db95074c386\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dc4b43688aab3000a6413dc37f6950d\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dc4b43688aab3000a6413dc37f6950d\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0d50fcce07af1ead5742ece71d1888\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d0d50fcce07af1ead5742ece71d1888\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21fd92cd239b05299148ceea32100a41\transformed\webkit-1.14.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21fd92cd239b05299148ceea32100a41\transformed\webkit-1.14.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51fe0542825ecabc89959ed91fa2eb7b\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51fe0542825ecabc89959ed91fa2eb7b\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afa70d7ce39e346b74ce167886d74b32\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afa70d7ce39e346b74ce167886d74b32\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4930c0bd9f0840ca3f2aec55fe66b3ba\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4930c0bd9f0840ca3f2aec55fe66b3ba\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c4b2ee7d9df4026106e59aeaf53665\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c4b2ee7d9df4026106e59aeaf53665\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-flowlayout:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e848cd9734c959e36fb6604681b34bf\transformed\jetified-accompanist-flowlayout-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-flowlayout:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e848cd9734c959e36fb6604681b34bf\transformed\jetified-accompanist-flowlayout-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c340e75db3c7f7d09682c74e0f91e20c\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c340e75db3c7f7d09682c74e0f91e20c\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\597f6b2804024b460781a93fcf31f95f\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\597f6b2804024b460781a93fcf31f95f\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8de82ca9d62301b610bf983e372b8908\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8de82ca9d62301b610bf983e372b8908\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05f3e78f103758134ece8fc3491b1fe1\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05f3e78f103758134ece8fc3491b1fe1\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19d7631cd25f8528b7593e9c691375e\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19d7631cd25f8528b7593e9c691375e\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4edd762e739d5bc65ce105b3810c5de6\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4edd762e739d5bc65ce105b3810c5de6\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e20db831a6bac46b665344bf988a429\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e20db831a6bac46b665344bf988a429\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9509a6a1252c7d12bbc8ae5935d9c1a3\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9509a6a1252c7d12bbc8ae5935d9c1a3\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df862a9921d17c0ba625a11f4b40c114\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df862a9921d17c0ba625a11f4b40c114\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6edcaf306b7b0bced34909e92576c6cf\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6edcaf306b7b0bced34909e92576c6cf\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-viewbinding:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c7ac3833900471d4d21486909525351\transformed\jetified-ui-viewbinding-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-viewbinding:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c7ac3833900471d4d21486909525351\transformed\jetified-ui-viewbinding-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32deb346123320764111514859f928c9\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32deb346123320764111514859f928c9\transformed\jetified-accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [io.sentry:sentry-android:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b28462348b8dcc29b68c5dfaf079ac8\transformed\jetified-sentry-android-7.22.5\AndroidManifest.xml:6:5-8:57
MERGED from [io.sentry:sentry-android:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b28462348b8dcc29b68c5dfaf079ac8\transformed\jetified-sentry-android-7.22.5\AndroidManifest.xml:6:5-8:57
MERGED from [io.sentry:sentry-android-ndk:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\899c67490b49a33163f75fb2d72293e0\transformed\jetified-sentry-android-ndk-7.22.5\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-ndk:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\899c67490b49a33163f75fb2d72293e0\transformed\jetified-sentry-android-ndk-7.22.5\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d71b6bc6636049cfa91710404141c520\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d71b6bc6636049cfa91710404141c520\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\127c8997e725075c8cf5027c0364411a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\127c8997e725075c8cf5027c0364411a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddc7eee06e745d0473de67fa65b3e624\transformed\jetified-play-services-measurement-sdk-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddc7eee06e745d0473de67fa65b3e624\transformed\jetified-play-services-measurement-sdk-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35ec57673ddb3c10dc99eb500fc832d6\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35ec57673ddb3c10dc99eb500fc832d6\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf9f47a57c2bd3aeac1de24bdec1db6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf9f47a57c2bd3aeac1de24bdec1db6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5369eeab582eb175a9b38824535717c\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5369eeab582eb175a9b38824535717c\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82a3c25d40028c313a02ab0084b17eaf\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82a3c25d40028c313a02ab0084b17eaf\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b83abd0a9e7e879a02141c74a773cac\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b83abd0a9e7e879a02141c74a773cac\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf9bf17b0072a132048e25f5106dae1b\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf9bf17b0072a132048e25f5106dae1b\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b664caaab4e5d4976de1b02959dcfec\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b664caaab4e5d4976de1b02959dcfec\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25b7098eba539877dc42cbc749a2fd5d\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25b7098eba539877dc42cbc749a2fd5d\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d4e59964faa5313026d1d45728924a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f2d4e59964faa5313026d1d45728924a\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0776b91425488ac8dce6808c5c5afbb\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0776b91425488ac8dce6808c5c5afbb\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c91034905ca39efe69a659a8881112d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c91034905ca39efe69a659a8881112d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c2dcae86878172fff40a4ddb686c11f\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c2dcae86878172fff40a4ddb686c11f\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82c86372c359925109c1930e4339d10c\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82c86372c359925109c1930e4339d10c\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92d4bd8399e6042d15ccd64b994d2bb7\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92d4bd8399e6042d15ccd64b994d2bb7\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f75e2f5027557a76790a63619407dae\transformed\jetified-runtime-livedata-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f75e2f5027557a76790a63619407dae\transformed\jetified-runtime-livedata-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5cc81aad252b6789c136987d5fcc9afd\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5cc81aad252b6789c136987d5fcc9afd\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b96e6857e29018a0c6f00798a7b9527\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b96e6857e29018a0c6f00798a7b9527\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1537d00f1b5f2aaaaaac3c5273cbb71a\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1537d00f1b5f2aaaaaac3c5273cbb71a\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8be9d2d36d9839991de1c4e2f54c1b\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc8be9d2d36d9839991de1c4e2f54c1b\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85bae30ed434eb28baef6b00d446b2ed\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\85bae30ed434eb28baef6b00d446b2ed\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4da8075ef694eaadf8347e2b12eeb10f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4da8075ef694eaadf8347e2b12eeb10f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db842c481bbb035cc265114857c3c788\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db842c481bbb035cc265114857c3c788\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6579377b27e61a245a3a7ddc2dd463c0\transformed\browser-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6579377b27e61a245a3a7ddc2dd463c0\transformed\browser-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18a1404a737ab3a298b99291a478e140\transformed\jetified-fresco-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18a1404a737ab3a298b99291a478e140\transformed\jetified-fresco-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0397f2bc77d60935dd99b4569cb9a30\transformed\jetified-imagepipeline-okhttp3-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0397f2bc77d60935dd99b4569cb9a30\transformed\jetified-imagepipeline-okhttp3-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e9ef0be0c745c814fe2609cca78a464\transformed\jetified-drawee-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e9ef0be0c745c814fe2609cca78a464\transformed\jetified-drawee-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc0d2e53812fffe2667b8800ca004098\transformed\jetified-nativeimagefilters-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc0d2e53812fffe2667b8800ca004098\transformed\jetified-nativeimagefilters-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\086f94370da8231ed853773680d29815\transformed\jetified-memory-type-native-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\086f94370da8231ed853773680d29815\transformed\jetified-memory-type-native-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b9a7e377997e430a61a4310adb5128f\transformed\jetified-memory-type-java-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b9a7e377997e430a61a4310adb5128f\transformed\jetified-memory-type-java-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\330e3da485a776b2f05e460664418c05\transformed\jetified-imagepipeline-native-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\330e3da485a776b2f05e460664418c05\transformed\jetified-imagepipeline-native-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cce25d7c66728d63ac33a18c542ab57\transformed\jetified-memory-type-ashmem-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6cce25d7c66728d63ac33a18c542ab57\transformed\jetified-memory-type-ashmem-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28ccb09a016b41ca77c5763dbd2922b4\transformed\jetified-imagepipeline-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28ccb09a016b41ca77c5763dbd2922b4\transformed\jetified-imagepipeline-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4832e9dbf7628a73d4014617e3bad2ad\transformed\jetified-nativeimagetranscoder-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4832e9dbf7628a73d4014617e3bad2ad\transformed\jetified-nativeimagetranscoder-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf1fcc162134d806881bfe348e62b6f4\transformed\jetified-imagepipeline-base-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf1fcc162134d806881bfe348e62b6f4\transformed\jetified-imagepipeline-base-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed5ff9cd8c2cd3bf0a9067a73ac73903\transformed\jetified-urimod-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed5ff9cd8c2cd3bf0a9067a73ac73903\transformed\jetified-urimod-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aaa522d0ab367a168809ecd9d90b144\transformed\jetified-vito-source-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aaa522d0ab367a168809ecd9d90b144\transformed\jetified-vito-source-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1f8024089fd6fea3a635a33c9f80c46\transformed\jetified-middleware-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1f8024089fd6fea3a635a33c9f80c46\transformed\jetified-middleware-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d091dd8b81a90400a1492fe6ef6b845e\transformed\jetified-ui-common-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d091dd8b81a90400a1492fe6ef6b845e\transformed\jetified-ui-common-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\132669564a13df33aedfc9003f1b23b0\transformed\jetified-soloader-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\132669564a13df33aedfc9003f1b23b0\transformed\jetified-soloader-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f5fd51b96bc8f1b6d54d4b2591c601c\transformed\jetified-fbcore-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f5fd51b96bc8f1b6d54d4b2591c601c\transformed\jetified-fbcore-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2d669ee9caeba2a508b7ca6e7fc4e1b\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2d669ee9caeba2a508b7ca6e7fc4e1b\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7f76f78c978e674a1336968dc051831\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f7f76f78c978e674a1336968dc051831\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed10a01b69c034e4c815313926b71830\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed10a01b69c034e4c815313926b71830\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c1148e615e12339891741fb52f575da\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c1148e615e12339891741fb52f575da\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb7838a6391355fcaf95d7a87d1ab860\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb7838a6391355fcaf95d7a87d1ab860\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad20a6d6d8b2027aa19b78125ef41da\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ad20a6d6d8b2027aa19b78125ef41da\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17a8ba29fd77033619df6eec30e346f5\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\17a8ba29fd77033619df6eec30e346f5\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abf2184437801464e9b88dea2c2d0764\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abf2184437801464e9b88dea2c2d0764\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\feab3837c1711f87bcf2772443a3e944\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\feab3837c1711f87bcf2772443a3e944\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c81ed56bb7cc3530b8a9d137342d0414\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c81ed56bb7cc3530b8a9d137342d0414\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd6d437a67db0f3b827c540bf0a1bbcd\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd6d437a67db0f3b827c540bf0a1bbcd\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22e8e4273a79efbd453bda6c4ecb77c9\transformed\jetified-room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22e8e4273a79efbd453bda6c4ecb77c9\transformed\jetified-room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58057c61ab78d9bdf8d473ce6e8d5c2e\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58057c61ab78d9bdf8d473ce6e8d5c2e\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1df0ef958be93b2f6a07daeefeadec68\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1df0ef958be93b2f6a07daeefeadec68\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b6b7c5fcece6be727a91b3529c36c17\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b6b7c5fcece6be727a91b3529c36c17\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b3192b9ba86b0cc71684aa361909b45\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b3192b9ba86b0cc71684aa361909b45\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b54621a91cc90f8c923b66f6b8e760ab\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b54621a91cc90f8c923b66f6b8e760ab\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ac6e233e752b126ef60b054dc6868aa\transformed\jetified-play-services-measurement-base-22.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ac6e233e752b126ef60b054dc6868aa\transformed\jetified-play-services-measurement-base-22.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3184320098644e8a0b31ce89621b4fc8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3184320098644e8a0b31ce89621b4fc8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfcf7b83cc9daae29a723dcdc9831e0b\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bfcf7b83cc9daae29a723dcdc9831e0b\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6676c797b507573c5383e5d2dd1fe087\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6676c797b507573c5383e5d2dd1fe087\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d2108b98508c4ee55e20557db7a7233\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d2108b98508c4ee55e20557db7a7233\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df95c34d4174f67f900e212e9ebc6f7f\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df95c34d4174f67f900e212e9ebc6f7f\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea3f756f26d5ac40fd2f114f988d5982\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea3f756f26d5ac40fd2f114f988d5982\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dc46601abed3bc5f006f747cfcd35d8\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dc46601abed3bc5f006f747cfcd35d8\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\669e178a604925a8e79c04477f68672a\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\669e178a604925a8e79c04477f68672a\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84ed9d443529fc901d30d5f764ef1143\transformed\jetified-ui-core-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84ed9d443529fc901d30d5f764ef1143\transformed\jetified-ui-core-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d8283ef7ea45a9cfcb87061c28904e\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90d8283ef7ea45a9cfcb87061c28904e\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-replay:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\929ecc092120e7d0f027953e1f728924\transformed\jetified-sentry-android-replay-7.22.5\AndroidManifest.xml:5:5-44
MERGED from [io.sentry:sentry-android-replay:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\929ecc092120e7d0f027953e1f728924\transformed\jetified-sentry-android-replay-7.22.5\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d72cf12e5c658a0cbb03db79daed6a5\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d72cf12e5c658a0cbb03db79daed6a5\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.77.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a84ae05983792e0a15d8128200f58f93\transformed\jetified-hermes-android-0.77.2-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.77.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a84ae05983792e0a15d8128200f58f93\transformed\jetified-hermes-android-0.77.2-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10e7939f59a050be744a3e1c3aa365bd\transformed\jetified-viewbinding-8.7.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10e7939f59a050be744a3e1c3aa365bd\transformed\jetified-viewbinding-8.7.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a032288e50448a28ff486b801527711d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a032288e50448a28ff486b801527711d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ae34c9f6b2fdbf5353d97222c9d76fe\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ae34c9f6b2fdbf5353d97222c9d76fe\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cf8925ed9e70192a1148a4203062da3\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cf8925ed9e70192a1148a4203062da3\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c16c7682157e8b13b48c5d11ce9345b9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c16c7682157e8b13b48c5d11ce9345b9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d064d13c0c8ffce8f37749721547f1d5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d064d13c0c8ffce8f37749721547f1d5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [:react-native-image-resizer] D:\Projects\EasyCura\frontend\node_modules\react-native-image-resizer\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-image-resizer] D:\Projects\EasyCura\frontend\node_modules\react-native-image-resizer\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17db74a5889927a25444fece673073a\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b17db74a5889927a25444fece673073a\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b458a5f379eaf7c5df7fade24d336f17\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b458a5f379eaf7c5df7fade24d336f17\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abbc9c196435969e40e7321b3a078e5e\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abbc9c196435969e40e7321b3a078e5e\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afba68633ab233904518bfa16556311f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afba68633ab233904518bfa16556311f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df1596ce48052a30ec6545621db4794c\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df1596ce48052a30ec6545621db4794c\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51c6bc2011c8a2b666f74883e5b7bb7f\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51c6bc2011c8a2b666f74883e5b7bb7f\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0223835a7c285e66b46bffb08c7d6f\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd0223835a7c285e66b46bffb08c7d6f\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ed255acdc3028ccafc15b3aea8550a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5ed255acdc3028ccafc15b3aea8550a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f91ceaa7cbc96fdd1a6169d0cdf01b8\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f91ceaa7cbc96fdd1a6169d0cdf01b8\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\718d55ff7c39f613e7d97f9c231924d3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\718d55ff7c39f613e7d97f9c231924d3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4dde0f6fb527e120b78758b336480cd\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4dde0f6fb527e120b78758b336480cd\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ff1ace1eea3ad38bda3a3a2fff12cb6\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ff1ace1eea3ad38bda3a3a2fff12cb6\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b985f2eda2053ae8e2f5db6fe8cc1570\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b985f2eda2053ae8e2f5db6fe8cc1570\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a3ec980e60ad6848d5369074a15b3da\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a3ec980e60ad6848d5369074a15b3da\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:5:5-7:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e3efb82e6a6d5499bf07e0e92ffee8d\transformed\jetified-fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e3efb82e6a6d5499bf07e0e92ffee8d\transformed\jetified-fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b198ecf16b59ae0079af471be112cc6b\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b198ecf16b59ae0079af471be112cc6b\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9fa070465237736c228043f51912a60\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9fa070465237736c228043f51912a60\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:5:5-7:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from D:\Projects\EasyCura\frontend\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Projects\EasyCura\frontend\android\app\src\debug\AndroidManifest.xml
queries
ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:15
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:10:5-18:15
MERGED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:10:5-18:15
MERGED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:8:5-12:15
MERGED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:8:5-12:15
MERGED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:12:5-16:15
MERGED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:12:5-16:15
MERGED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:27:5-30:15
MERGED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:27:5-30:15
intent#action:name:org.chromium.intent.action.PAY
ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
action#org.chromium.intent.action.PAY
ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-69
	android:name
		ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-66
intent#action:name:org.chromium.intent.action.IS_READY_TO_PAY
ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:18
action#org.chromium.intent.action.IS_READY_TO_PAY
ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-81
	android:name
		ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:21-78
intent#action:name:org.chromium.intent.action.UPDATE_PAYMENT_DETAILS
ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-16:18
action#org.chromium.intent.action.UPDATE_PAYMENT_DETAILS
ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-88
	android:name
		ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:21-85
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-28:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-47
	android:authorities
		ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-64
	android:exported
		ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-37
	android:name
		ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
	android:resource
		ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
	android:name
		ADDED from [:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
uses-permission#android.permission.WAKE_LOCK
ADDED from [:react-native-firebase_auth] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\953341a889a6c2f329f0224271e451e0\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\953341a889a6c2f329f0224271e451e0\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [:react-native-firebase_auth] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
meta-data#app_data_collection_default_enabled
ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
	android:value
		ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
	android:name
		ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1a6287ec5705adcba52186458007b35\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1a6287ec5705adcba52186458007b35\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afba68633ab233904518bfa16556311f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afba68633ab233904518bfa16556311f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	tools:targetApi
		ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-32
	android:directBootAware
		ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
	android:name
		ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
meta-data#com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar
ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
provider#io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider
ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
	android:authorities
		ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
	android:exported
		ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
	android:initOrder
		ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
	android:name
		ADDED from [:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
meta-data#io.sentry.auto-init
ADDED from [:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:37
	android:value
		ADDED from [:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-34
	android:name
		ADDED from [:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
provider#com.imagepicker.ImagePickerProvider
ADDED from [:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-71
	android:exported
		ADDED from [:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-63
provider#cl.json.RNShareFileProvider
ADDED from [:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-72
	android:exported
		ADDED from [:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-55
intent#action:name:android.intent.action.VIEW+data:scheme:http
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:13:9-17:18
action#android.intent.action.VIEW
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:14:13-65
	android:name
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:14:21-62
data
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:13-43
	android:scheme
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:19-40
activity#com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:21:9-65:20
	android:launchMode
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:24:13-44
	android:exported
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:23:13-36
	android:name
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:22:13-109
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth-redirect+data:host:link-accounts+data:host:link-accounts+data:host:link-accounts+data:host:link-native-accounts+data:host:native-redirect+data:path:/${applicationId}/cancel+data:path:/${applicationId}/success+data:pathPrefix:/${applicationId}+data:pathPrefix:/${applicationId}+data:pathPrefix:/${applicationId}/authentication_return+data:pathPrefix:/${applicationId}/authentication_return+data:scheme:stripe+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:25:13-64:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth-redirect+data:host:link-accounts+data:host:link-accounts+data:host:link-accounts+data:host:link-native-accounts+data:host:native-redirect+data:path:/com.easycura/cancel+data:path:/com.easycura/success+data:pathPrefix:/com.easycura+data:pathPrefix:/com.easycura+data:pathPrefix:/com.easycura/authentication_return+data:pathPrefix:/com.easycura/authentication_return+data:scheme:stripe+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:25:13-64:29
category#android.intent.category.DEFAULT
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:28:17-76
	android:name
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:28:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:29:17-78
	android:name
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:29:27-75
activity#com.stripe.android.financialconnections.FinancialConnectionsSheetActivity
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:66:9-69:77
	android:exported
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:68:13-37
	android:theme
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:69:13-74
	android:name
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:67:13-101
activity#com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity
ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:70:9-74:58
	android:windowSoftInputMode
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:74:13-55
	android:exported
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:72:13-37
	android:theme
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:73:13-74
	android:name
		ADDED from [com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:71:13-110
activity#com.stripe.android.paymentsheet.PaymentSheetActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:8:9-11:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:11:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:9:13-80
activity#com.stripe.android.paymentsheet.PaymentOptionsActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:12:9-15:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:14:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:15:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:13:13-82
activity#com.stripe.android.customersheet.CustomerSheetActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:16:9-19:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:18:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:19:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:17:13-82
activity#com.stripe.android.paymentsheet.addresselement.AddressElementActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:20:9-23:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:22:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:21:13-97
activity#com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:24:9-27:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:27:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:25:13-118
activity#com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:28:9-31:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:30:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:31:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:29:13-105
activity#com.stripe.android.paymentsheet.ui.SepaMandateActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:32:9-35:69
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:35:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:33:13-82
activity#com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:36:9-39:68
	android:exported
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:38:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:37:13-94
activity#com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity
ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:40:9-42:69
	android:theme
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:42:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:41:13-121
activity#com.stripe.android.link.LinkActivity
ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:8:9-13:61
	android:launchMode
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:12:13-43
	android:autoRemoveFromRecents
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:10:13-49
	android:configChanges
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:11:13-115
	android:theme
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:13:13-58
	android:name
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:9:13-64
activity#com.stripe.android.link.LinkForegroundActivity
ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:14:9-19:61
	android:launchMode
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:18:13-43
	android:autoRemoveFromRecents
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:16:13-49
	android:configChanges
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:17:13-115
	android:theme
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:19:13-58
	android:name
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:15:13-74
activity#com.stripe.android.link.LinkRedirectHandlerActivity
ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:20:9-37:20
	android:launchMode
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:24:13-48
	android:autoRemoveFromRecents
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:22:13-49
	android:exported
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:23:13-36
	android:theme
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:25:13-58
	android:name
		ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:21:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:complete+data:path:/${applicationId}+data:scheme:link-popup
ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:26:13-36:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:complete+data:path:/com.easycura+data:scheme:link-popup
ADDED from [com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:26:13-36:29
activity#com.stripe.android.ui.core.cardscan.CardScanActivity
ADDED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9cce331ffb64663587db3c3239c1f\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:8:9-11:69
	android:exported
		ADDED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9cce331ffb64663587db3c3239c1f\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9cce331ffb64663587db3c3239c1f\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:11:13-66
	android:name
		ADDED from [com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9cce331ffb64663587db3c3239c1f\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:9:13-80
package#com.android.chrome
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:11:9-54
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:11:18-51
activity#com.stripe.android.view.AddPaymentMethodActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:15:9-18:57
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:17:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:18:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:16:13-76
activity#com.stripe.android.view.PaymentMethodsActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:19:9-22:57
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:22:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:20:13-74
activity#com.stripe.android.view.PaymentFlowActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:23:9-26:57
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:26:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:24:13-71
activity#com.stripe.android.view.PaymentAuthWebViewActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:27:9-30:57
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:29:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:30:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:28:13-78
activity#com.stripe.android.view.PaymentRelayActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:31:9-34:61
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:33:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:34:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:32:13-72
activity#com.stripe.android.payments.StripeBrowserLauncherActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:40:9-44:61
	android:launchMode
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:43:13-44
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:42:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:44:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:41:13-85
activity#com.stripe.android.payments.StripeBrowserProxyReturnActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:45:9-62:20
	android:launchMode
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:48:13-44
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:47:13-36
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:49:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:46:13-88
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:payment_return_url+data:path:/${applicationId}+data:scheme:stripesdk
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:50:13-61:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:payment_return_url+data:path:/com.easycura+data:scheme:stripesdk
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:50:13-61:29
activity#com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:63:9-66:57
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:65:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:66:13-54
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:64:13-114
activity#com.stripe.android.googlepaylauncher.GooglePayLauncherActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:67:9-70:66
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:69:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:70:13-63
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:68:13-90
activity#com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:71:9-74:66
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:73:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:74:13-63
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:72:13-103
activity#com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:75:9-78:68
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:77:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:78:13-65
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:76:13-107
activity#com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity
ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:79:9-82:61
	android:exported
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:81:13-37
	android:theme
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:82:13-58
	android:name
		ADDED from [com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:80:13-97
activity#com.stripe.android.stripe3ds2.views.ChallengeActivity
ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61660682525456ea1e61825c06c8a332\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:8:9-11:54
	android:exported
		ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61660682525456ea1e61825c06c8a332\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61660682525456ea1e61825c06c8a332\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:11:13-51
	android:name
		ADDED from [com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61660682525456ea1e61825c06c8a332\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:9:13-81
meta-data#com.google.android.gms.version
ADDED from [com.google.maps.android:android-maps-utils:3.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52c8b1235080f34adfa9f8a8d6897eed\transformed\jetified-android-maps-utils-3.10.0\AndroidManifest.xml:23:9-25:69
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3184320098644e8a0b31ce89621b4fc8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3184320098644e8a0b31ce89621b4fc8\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.maps.android:android-maps-utils:3.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52c8b1235080f34adfa9f8a8d6897eed\transformed\jetified-android-maps-utils-3.10.0\AndroidManifest.xml:25:13-66
	android:name
		ADDED from [com.google.maps.android:android-maps-utils:3.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52c8b1235080f34adfa9f8a8d6897eed\transformed\jetified-android-maps-utils-3.10.0\AndroidManifest.xml:24:13-58
intent#action:name:com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:13:9-15:18
action#com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:14:13-91
	android:name
		ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:14:21-88
meta-data#com.google.android.play.billingclient.version
ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:19:9-21:37
	android:value
		ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:21:13-34
	android:name
		ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:20:13-73
activity#com.android.billingclient.api.ProxyBillingActivity
ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:23:9-27:75
	android:exported
		ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:26:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:25:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:24:13-78
activity#com.android.billingclient.api.ProxyBillingActivityV2
ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:28:9-32:75
	android:exported
		ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:31:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:30:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:32:13-72
	android:name
		ADDED from [com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:29:13-80
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:35:13-45:29
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:70:17-109
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:22:5-24:33
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:23:8-40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:24:8-31
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:29:7-61
	android:name
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:29:16-59
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:33:7-86
	android:required
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:33:59-83
	android:name
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:33:21-58
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:23:22-74
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\953341a889a6c2f329f0224271e451e0\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\953341a889a6c2f329f0224271e451e0\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:61:17-119
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0aca4c1084c580fd84a1e342864f2bf\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0aca4c1084c580fd84a1e342864f2bf\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0aca4c1084c580fd84a1e342864f2bf\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0aca4c1084c580fd84a1e342864f2bf\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b54621a91cc90f8c923b66f6b8e760ab\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b54621a91cc90f8c923b66f6b8e760ab\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2635bfc0e7ffd0412d0b18fe22bc5174\transformed\jetified-play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1a6287ec5705adcba52186458007b35\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1a6287ec5705adcba52186458007b35\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1a6287ec5705adcba52186458007b35\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50d26564deea5dfafdf74f06065c2972\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50d26564deea5dfafdf74f06065c2972\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f9e75e8f31982799eee1bae3519949\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f9e75e8f31982799eee1bae3519949\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c91034905ca39efe69a659a8881112d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c91034905ca39efe69a659a8881112d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d064d13c0c8ffce8f37749721547f1d5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d064d13c0c8ffce8f37749721547f1d5\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f9e75e8f31982799eee1bae3519949\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f9e75e8f31982799eee1bae3519949\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f9e75e8f31982799eee1bae3519949\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
provider#io.sentry.android.core.SentryInitProvider
ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:12:9-15:40
	android:authorities
		ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:14:13-70
	android:exported
		ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:15:13-37
	android:name
		ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:13:13-69
provider#io.sentry.android.core.SentryPerformanceProvider
ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:16:9-20:39
	android:authorities
		ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:18:13-77
	android:exported
		ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:19:13-37
	android:initOrder
		ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:20:13-36
	android:name
		ADDED from [io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:17:13-76
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed22562585fdad680a75fda99f04d119\transformed\jetified-play-services-measurement-impl-22.0.2\AndroidManifest.xml:26:5-110
MERGED from [com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a3ec980e60ad6848d5369074a15b3da\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:9:5-110
MERGED from [com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a3ec980e60ad6848d5369074a15b3da\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:40:13-87
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c91034905ca39efe69a659a8881112d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c91034905ca39efe69a659a8881112d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c91034905ca39efe69a659a8881112d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.easycura.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.easycura.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1df0ef958be93b2f6a07daeefeadec68\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1df0ef958be93b2f6a07daeefeadec68\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1df0ef958be93b2f6a07daeefeadec68\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dc46601abed3bc5f006f747cfcd35d8\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dc46601abed3bc5f006f747cfcd35d8\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dc46601abed3bc5f006f747cfcd35d8\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dc46601abed3bc5f006f747cfcd35d8\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dc46601abed3bc5f006f747cfcd35d8\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afba68633ab233904518bfa16556311f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afba68633ab233904518bfa16556311f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afba68633ab233904518bfa16556311f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b198ecf16b59ae0079af471be112cc6b\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b198ecf16b59ae0079af471be112cc6b\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b198ecf16b59ae0079af471be112cc6b\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:13:13-57
meta-data#aia-compat-api-min-version
ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9fa070465237736c228043f51912a60\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
	android:value
		ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9fa070465237736c228043f51912a60\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
	android:name
		ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9fa070465237736c228043f51912a60\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
