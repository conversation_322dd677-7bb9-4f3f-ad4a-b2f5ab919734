import {Platform, PermissionsAndroid, Alert} from 'react-native';
import Geolocation from '@react-native-community/geolocation';
import {strings} from '../translation/strings';
import {it} from '../translation/supportedlanguage';

export interface LocationPermissionResult {
  granted: boolean;
  error?: string;
}

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
}

export interface LocationOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
}

const DEFAULT_LOCATION_OPTIONS: LocationOptions = {
  enableHighAccuracy: true,
  timeout: 20000,
  maximumAge: 1000,
};

export const checkLocationPermissions = async (): Promise<boolean> => {
  if (Platform.OS === 'ios') {
    return true;
  }

  try {
    const fineLocationGranted = await PermissionsAndroid.check(
      PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
    );
    const coarseLocationGranted = await PermissionsAndroid.check(
      PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
    );

    return fineLocationGranted || coarseLocationGranted;
  } catch (error) {
    console.error('Error checking location permissions:', error);
    return false;
  }
};

export const requestLocationPermissions = async (): Promise<LocationPermissionResult> => {
  if (Platform.OS === 'ios') {
    return new Promise((resolve) => {
      Geolocation.requestAuthorization(
        () => {
          resolve({ granted: true });
        },
        (error) => {
          console.error('iOS location permission denied:', error);
          resolve({ 
            granted: false, 
            error: 'Location permission denied on iOS' 
          });
        }
      );
    });
  }

  try {
    const alreadyGranted = await checkLocationPermissions();
    if (alreadyGranted) {
      return { granted: true };
    }

    const fine = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      {
        title: 'Location Permission',
        message: 'EasyCura needs access to your location to find nearby healthcare professionals and provide accurate address information.',
        buttonNeutral: 'Ask Me Later',
        buttonNegative: 'Cancel',
        buttonPositive: 'OK',
      }
    );

    const coarse = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
      {
        title: 'Location Permission',
        message: 'EasyCura needs access to your location to find nearby healthcare professionals and provide accurate address information.',
        buttonNeutral: 'Ask Me Later',
        buttonNegative: 'Cancel',
        buttonPositive: 'OK',
      }
    );

    const granted =
      fine === PermissionsAndroid.RESULTS.GRANTED ||
      coarse === PermissionsAndroid.RESULTS.GRANTED;

    return { 
      granted,
      error: granted ? undefined : 'Location permission denied on Android'
    };
  } catch (error) {
    console.error('Error requesting location permissions:', error);
    return { 
      granted: false, 
      error: `Permission request failed: ${error}` 
    };
  }
};

export const getCurrentPosition = (
  options: LocationOptions = DEFAULT_LOCATION_OPTIONS
): Promise<LocationCoordinates> => {
  return new Promise((resolve, reject) => {
    Geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        resolve({ latitude, longitude });
      },
      (error) => {
        console.error('getCurrentPosition error:', error);
        let errorMessage = 'Unable to get current location';
        
        switch (error.code) {
          case 1:
            errorMessage = 'Location permission denied';
            break;
          case 2:
            errorMessage = 'Location unavailable';
            break;
          case 3:
            errorMessage = 'Location request timed out';
            break;
          default:
            errorMessage = `Location error: ${error.message}`;
        }
        
        reject(new Error(errorMessage));
      },
      options
    );
  });
};

export const requestLocationAndGetPosition = async (
  options: LocationOptions = DEFAULT_LOCATION_OPTIONS
): Promise<LocationCoordinates> => {
  const permissionResult = await requestLocationPermissions();
  
  if (!permissionResult.granted) {
    throw new Error(permissionResult.error || 'Location permission denied');
  }

  return getCurrentPosition(options);
};

export const showLocationPermissionDeniedAlert = () => {
  Alert.alert(
    strings(it.attention),
    strings(it.errorMissedPermissionsPosition),
    [
      {
        text: 'OK',
        style: 'default',
      },
    ]
  );
};

export const showLocationErrorAlert = (message?: string) => {
  Alert.alert(
    strings(it.attention),
    message || strings(it.homepagePatientPositionNotFound),
    [
      {
        text: 'OK',
        style: 'default',
      },
    ]
  );
};
