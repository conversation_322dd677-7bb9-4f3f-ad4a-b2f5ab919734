# ninja log v5
3	25	0	D:/Projects/EasyCura/frontend/node_modules/react-native-reanimated/android/.cxx/Debug/f75j4i29/x86/CMakeFiles/cmake.verify_globs	1ff050a8717af209
14	2755	7766695327339259	src/main/cpp/worklets/CMakeFiles/worklets.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o	71c13011027113c3
22	3074	7766695330446141	src/main/cpp/worklets/CMakeFiles/worklets.dir/f40d19c2ea714cd08f3adae06bedaa3c/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp.o	d67a364f484fcaa2
40	3083	7766695330631271	src/main/cpp/worklets/CMakeFiles/worklets.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o	5f0c04420376f6e2
27	3139	7766695331111531	src/main/cpp/worklets/CMakeFiles/worklets.dir/f40d19c2ea714cd08f3adae06bedaa3c/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp.o	53f707af490e3714
51	3185	7766695331376667	src/main/cpp/worklets/CMakeFiles/worklets.dir/f40d19c2ea714cd08f3adae06bedaa3c/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp.o	d84c87c048cc47c
35	3220	7766695331617008	src/main/cpp/worklets/CMakeFiles/worklets.dir/f40d19c2ea714cd08f3adae06bedaa3c/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp.o	7abbcefc667577f5
58	3497	7766695334398777	src/main/cpp/worklets/CMakeFiles/worklets.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o	a30d5be4a52c74c0
31	3526	7766695333979192	src/main/cpp/worklets/CMakeFiles/worklets.dir/f40d19c2ea714cd08f3adae06bedaa3c/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp.o	7e93f4c1991a939c
69	3560	7766695335075559	src/main/cpp/worklets/CMakeFiles/worklets.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp.o	a58111007c4b9640
3	3695	7766695335746522	src/main/cpp/worklets/CMakeFiles/worklets.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o	ee8686010f727695
64	4124	7766695340723822	src/main/cpp/worklets/CMakeFiles/worklets.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o	922b2bcd69c872b6
7	4430	7766695343945767	src/main/cpp/worklets/CMakeFiles/worklets.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o	a4766b559394f888
80	4508	7766695344375772	src/main/cpp/worklets/CMakeFiles/worklets.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o	21ae42a9af02c708
18	4629	7766695345195768	src/main/cpp/worklets/CMakeFiles/worklets.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/worklets/SharedItems/Shareables.cpp.o	db392a39b217006c
74	5047	7766695350089064	src/main/cpp/worklets/CMakeFiles/worklets.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o	bc0aebfb4902f25e
3140	5315	7766695352115948	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o	76d5fbd949f11c76
46	6160	7766695359467443	src/main/cpp/worklets/CMakeFiles/worklets.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/worklets/Tools/ReanimatedVersion.cpp.o	d766e05e8723620e
3526	7260	7766695371452849	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o	1808703861969b71
3695	7783	7766695377227487	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/f407c799fd26d64b3946d6ad0478b5dd/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o	b8950c1179e1f4d
10	8808	7766695387160599	src/main/cpp/worklets/CMakeFiles/worklets.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o	5e8fff537389ed8d
3084	9837	7766695397996399	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o	f98cafc2623fff09
4430	9873	7766695398266396	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/f407c799fd26d64b3946d6ad0478b5dd/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o	a6acf37bb050c6c9
3075	10206	7766695400393929	src/main/cpp/worklets/CMakeFiles/worklets.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o	847b66852caa9fcb
87	10525	7766695404655157	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o	52371ab86d203bde
8811	10888	7766695408667517	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/reanimated/Tools/FeaturesConfig.cpp.o	a154d574249111d0
3185	11146	7766695411163568	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/reanimated/Fabric/PropsRegistry.cpp.o	c754ab66c571ebe2
6160	11658	7766695416333287	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o	921cf030ed84b980
3220	11998	7766695419527971	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o	cbcd2a8ba5ce4ccd
5315	12181	7766695421624045	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o	b4fd3da16e26e659
2760	12364	7766695423472622	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o	b58ecafcbfd5a126
3498	12772	7766695427549542	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o	8ed34aaf8dac819f
4630	12816	7766695427956865	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o	9284ff31cd4c4760
3561	12877	7766695428616172	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o	a404047e076ea123
12364	12878	7766695428735662	../../../../build/intermediates/cxx/Debug/f75j4i29/obj/x86/libworklets.so	e33a42f21242a194
7784	13330	7766695433194928	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o	54affc170ce2820e
4509	13336	7766695433243903	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o	ed49de9a919c1e4
5047	15183	7766695451672986	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o	2b7eb3b8c918ebfd
9838	15814	7766695458190755	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o	6f2d8402700d4912
4125	16611	7766695465814633	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/e1b0cba2ffba4db10cdaa2cd60b19764/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o	b2b2cc0c37ace907
7261	17184	7766695471690322	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o	f624b7952c54c9b6
17184	17339	7766695473452045	../../../../build/intermediates/cxx/Debug/f75j4i29/obj/x86/libreanimated.so	cca494548e565bdb
2	51	0	D:/Projects/EasyCura/frontend/node_modules/react-native-reanimated/android/.cxx/Debug/f75j4i29/x86/CMakeFiles/cmake.verify_globs	1ff050a8717af209
3	44	0	D:/Projects/EasyCura/frontend/node_modules/react-native-reanimated/android/.cxx/Debug/f75j4i29/x86/CMakeFiles/cmake.verify_globs	1ff050a8717af209
3	34	0	D:/Projects/EasyCura/frontend/node_modules/react-native-reanimated/android/.cxx/Debug/f75j4i29/x86/CMakeFiles/cmake.verify_globs	1ff050a8717af209
