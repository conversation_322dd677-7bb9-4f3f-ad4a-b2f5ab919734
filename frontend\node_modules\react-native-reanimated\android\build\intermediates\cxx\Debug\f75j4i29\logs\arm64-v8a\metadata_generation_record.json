[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\f75j4i29\\arm64-v8a\\android_gradle_build.json' was up-to-date", "file_": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]