1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.easycura"
4    android:versionCode="4"
5    android:versionName="1.2.1" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:4:5-66
11-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.VIBRATE" />
12-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:5:5-65
12-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:5:22-63
13    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
13-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:6:5-80
13-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:6:22-78
14    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
14-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:7:5-78
14-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:7:22-76
15    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
15-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:8:5-80
15-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:8:22-78
16    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
16-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:9:5-84
16-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:9:22-82
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
17-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:10:5-78
17-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:10:22-76
18    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
18-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:11:5-75
18-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:11:22-73
19    <uses-permission android:name="android.permission.CAMERA" />
19-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:12:5-64
19-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:12:22-62
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:13:5-80
20-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:13:22-78
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:14:5-79
21-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:14:22-77
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
22-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:15:5-76
22-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:15:22-74
23    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
23-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:16:5-79
23-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:16:22-77
24    <uses-permission android:name="android.permission.RECORD_AUDIO" />
24-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:17:5-70
24-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:17:22-68
25    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
25-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:18:5-77
25-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:18:22-75
26    <uses-permission android:name="com.android.vending.BILLING" />
26-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:19:5-66
26-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:19:22-64
27
28    <queries>
28-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:15
29        <intent>
29-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
30            <action android:name="org.chromium.intent.action.PAY" />
30-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-69
30-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-66
31        </intent>
32        <intent>
32-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:18
33            <action android:name="org.chromium.intent.action.IS_READY_TO_PAY" />
33-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-81
33-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:21-78
34        </intent>
35        <intent>
35-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-16:18
36            <action android:name="org.chromium.intent.action.UPDATE_PAYMENT_DETAILS" />
36-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-88
36-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:21-85
37        </intent>
38        <!-- Added to check the default browser that will host the AuthFlow. -->
39        <intent>
39-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:13:9-17:18
40            <action android:name="android.intent.action.VIEW" />
40-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:14:13-65
40-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:14:21-62
41
42            <data android:scheme="http" />
42-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:13-43
42-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:19-40
43        </intent> <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
44        <package android:name="com.android.chrome" />
44-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:11:9-54
44-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:11:18-51
45
46        <intent>
46-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:13:9-15:18
47            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
47-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:14:13-91
47-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:14:21-88
48        </intent> <!-- Needs to be explicitly declared on Android R+ -->
49        <package android:name="com.google.android.apps.maps" />
49-->[com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:29:7-61
49-->[com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:29:16-59
50    </queries>
51
52    <uses-permission android:name="android.permission.WAKE_LOCK" />
52-->[:react-native-firebase_auth] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
52-->[:react-native-firebase_auth] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
53
54    <uses-feature
54-->[com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:22:5-24:33
55        android:glEsVersion="0x00020000"
55-->[com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:23:8-40
56        android:required="true" />
56-->[com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:24:8-31
57
58    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
58-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:23:5-77
58-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:23:22-74
59    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
59-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:5-82
59-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:26:22-79
60    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
60-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:25:5-79
60-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:25:22-76
61    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
61-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:26:5-88
61-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:26:22-85
62    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
62-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:27:5-82
62-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:27:22-79
63    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
63-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50d26564deea5dfafdf74f06065c2972\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
63-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50d26564deea5dfafdf74f06065c2972\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
64    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
64-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:26:5-110
64-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:26:22-107
65
66    <permission
66-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
67        android:name="com.easycura.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
67-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
68        android:protectionLevel="signature" />
68-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
69
70    <uses-permission android:name="com.easycura.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- for android -->
70-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
70-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
71    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
72    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
73    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
74    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
75    <!-- for Samsung -->
76    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
76-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
76-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
77    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
77-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
77-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
78    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
78-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
78-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
79    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
79-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
79-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
80    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
80-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
80-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
81    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
81-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
81-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
82    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
82-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
82-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
83    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
83-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
83-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
84    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
84-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
84-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
85    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
85-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
85-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
86    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
86-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
86-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
87    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
87-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
87-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
88    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
88-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
88-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
89    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
89-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
90    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
90-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
91    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
91-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6395a7c28645e0b7ff4007520c017893\transformed\jetified-ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
92
93    <application
93-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:21:5-71:19
94        android:name="com.easycura.MainApplication"
94-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:22:9-40
95        android:allowBackup="false"
95-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:26:9-36
96        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
96-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\922a10d756c52f0fea598b4dbfd19309\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
97        android:debuggable="true"
98        android:extractNativeLibs="false"
99        android:icon="@mipmap/ic_launcher"
99-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:24:9-43
100        android:label="@string/app_name"
100-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:23:9-41
101        android:roundIcon="@mipmap/ic_launcher_round"
101-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:25:9-54
102        android:supportsRtl="true"
102-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:30:9-35
103        android:theme="@style/AppTheme"
103-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:27:9-40
104        android:usesCleartextTraffic="true" >
104-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:28:9-44
105        <activity
105-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:32:9-85
106            android:name="com.facebook.react.devsupport.DevSettingsActivity"
106-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:32:19-83
107            android:exported="false" />
107-->[com.facebook.react:react-android:0.77.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a24672aa86199034f630fefd9c14b9\transformed\jetified-react-android-0.77.2-debug\AndroidManifest.xml:21:13-37
108        <activity
108-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:34:9-50:20
109            android:name="com.easycura.MainActivity"
109-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:35:13-41
110            android:allowBackup="false"
110-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:39:13-40
111            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
111-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:40:13-122
112            android:exported="true"
112-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:45:13-36
113            android:icon="@mipmap/ic_launcher"
113-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:37:13-47
114            android:label="@string/app_name"
114-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:36:13-45
115            android:launchMode="singleTask"
115-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:41:13-44
116            android:roundIcon="@mipmap/ic_launcher_round"
116-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:38:13-58
117            android:theme="@style/AppTheme"
117-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:43:13-44
118            android:usesCleartextTraffic="true"
118-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:44:13-48
119            android:windowSoftInputMode="adjustResize" >
119-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:42:13-55
120            <intent-filter>
120-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:46:13-49:29
121                <action android:name="android.intent.action.MAIN" />
121-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:47:17-68
121-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:47:25-66
122
123                <category android:name="android.intent.category.LAUNCHER" />
123-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:48:17-76
123-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:48:27-74
124            </intent-filter>
125        </activity>
126
127        <meta-data
127-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:52:9-120
128            android:name="com.dieam.reactnativepushnotification.notification_foreground"
128-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:52:20-96
129            android:value="false" />
129-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:52:97-118
130        <meta-data
130-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:53:9-125
131            android:name="com.dieam.reactnativepushnotification.notification_color"
131-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:53:20-91
132            android:resource="@color/white" />
132-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:53:92-123
133        <meta-data
133-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:54:9-123
134            android:name="com.google.android.geo.API_KEY"
134-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:54:20-65
135            android:value="AIzaSyD1x-sSbeZC2HDP8T-ldmzVVI5-M6DefcI" />
135-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:54:66-121
136
137        <receiver
137-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:56:9-132
138            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationActions"
138-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:56:19-105
139            android:exported="false" />
139-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:56:106-130
140        <receiver android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationPublisher" />
140-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:57:9-109
140-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:57:19-107
141        <receiver
141-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:58:9-64:20
142            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationBootEventReceiver"
142-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:58:19-115
143            android:exported="false" >
143-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:58:116-140
144            <intent-filter>
144-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:59:13-63:29
145                <action android:name="android.intent.action.BOOT_COMPLETED" />
145-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:60:17-78
145-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:60:25-76
146                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
146-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:61:17-81
146-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:61:25-79
147                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
147-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:62:17-81
147-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:62:25-79
148            </intent-filter>
149        </receiver>
150
151        <service
151-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:66:9-70:19
152            android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationListenerService"
152-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:66:18-112
153            android:exported="false" >
153-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:66:113-137
154            <intent-filter>
154-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:67:13-69:29
155                <action android:name="com.google.firebase.MESSAGING_EVENT" />
155-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:68:17-77
155-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:68:25-75
156            </intent-filter>
157        </service>
158
159        <provider
159-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-28:20
160            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
160-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-83
161            android:authorities="com.easycura.fileprovider"
161-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-64
162            android:exported="false"
162-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-37
163            android:grantUriPermissions="true" >
163-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-47
164            <meta-data
164-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
165                android:name="android.support.FILE_PROVIDER_PATHS"
165-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
166                android:resource="@xml/file_provider_paths" />
166-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
167        </provider>
168
169        <meta-data
169-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
170            android:name="app_data_collection_default_enabled"
170-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
171            android:value="true" />
171-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
172
173        <service
173-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
174            android:name="com.google.firebase.components.ComponentDiscoveryService"
174-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
175            android:directBootAware="true"
175-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
176            android:exported="false" >
176-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
177            <meta-data
177-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
178                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
178-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
179                android:value="com.google.firebase.components.ComponentRegistrar" />
179-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
180            <meta-data
180-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:69:13-71:85
181                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
181-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:70:17-109
182                android:value="com.google.firebase.components.ComponentRegistrar" />
182-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:71:17-82
183            <meta-data
183-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:57:13-59:85
184                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
184-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:58:17-122
185                android:value="com.google.firebase.components.ComponentRegistrar" />
185-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:59:17-82
186            <meta-data
186-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:60:13-62:85
187                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
187-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:61:17-119
188                android:value="com.google.firebase.components.ComponentRegistrar" />
188-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:62:17-82
189            <meta-data
189-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:37:13-39:85
190                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
190-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:38:17-139
191                android:value="com.google.firebase.components.ComponentRegistrar" />
191-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:39:17-82
192            <meta-data
192-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
193                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
193-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
194                android:value="com.google.firebase.components.ComponentRegistrar" />
194-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
195            <meta-data
195-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
196                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
196-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
197                android:value="com.google.firebase.components.ComponentRegistrar" />
197-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f436570aa57f77828181fae4664c98d8\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
198            <meta-data
198-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1a6287ec5705adcba52186458007b35\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
199                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
199-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1a6287ec5705adcba52186458007b35\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
200                android:value="com.google.firebase.components.ComponentRegistrar" />
200-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1a6287ec5705adcba52186458007b35\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
201            <meta-data
201-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
202                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
202-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
203                android:value="com.google.firebase.components.ComponentRegistrar" />
203-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
204            <meta-data
204-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afba68633ab233904518bfa16556311f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
205                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
205-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afba68633ab233904518bfa16556311f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
206                android:value="com.google.firebase.components.ComponentRegistrar" />
206-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afba68633ab233904518bfa16556311f\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
207        </service>
208
209        <provider
209-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
210            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
210-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
211            android:authorities="com.easycura.reactnativefirebaseappinitprovider"
211-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
212            android:exported="false"
212-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
213            android:initOrder="99" />
213-->[:react-native-firebase_app] D:\Projects\EasyCura\frontend\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
214
215        <meta-data
215-->[:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:37
216            android:name="io.sentry.auto-init"
216-->[:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
217            android:value="false" />
217-->[:sentry_react-native] D:\Projects\EasyCura\frontend\node_modules\@sentry\react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-34
218
219        <provider
219-->[:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
220            android:name="com.imagepicker.ImagePickerProvider"
220-->[:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-63
221            android:authorities="com.easycura.imagepickerprovider"
221-->[:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-71
222            android:exported="false"
222-->[:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
223            android:grantUriPermissions="true" >
223-->[:react-native-image-picker] D:\Projects\EasyCura\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
224            <meta-data
224-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
225                android:name="android.support.FILE_PROVIDER_PATHS"
225-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
226                android:resource="@xml/imagepicker_provider_paths" />
226-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
227        </provider>
228        <provider
228-->[:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
229            android:name="cl.json.RNShareFileProvider"
229-->[:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-55
230            android:authorities="com.easycura.rnshare.fileprovider"
230-->[:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-72
231            android:exported="false"
231-->[:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
232            android:grantUriPermissions="true" >
232-->[:react-native-share] D:\Projects\EasyCura\frontend\node_modules\react-native-share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
233            <meta-data
233-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
234                android:name="android.support.FILE_PROVIDER_PATHS"
234-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
235                android:resource="@xml/share_download_paths" />
235-->[:react-native-webview] D:\Projects\EasyCura\frontend\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
236        </provider>
237
238        <activity
238-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:21:9-65:20
239            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
239-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:22:13-109
240            android:exported="true"
240-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:23:13-36
241            android:launchMode="singleTask" >
241-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:24:13-44
242            <intent-filter>
242-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:25:13-64:29
243                <action android:name="android.intent.action.VIEW" />
243-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:14:13-65
243-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:14:21-62
244
245                <category android:name="android.intent.category.DEFAULT" />
245-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:28:17-76
245-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:28:27-73
246                <category android:name="android.intent.category.BROWSABLE" />
246-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:29:17-78
246-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:29:27-75
247
248                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
249                <data
249-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:13-43
250                    android:host="link-accounts"
251                    android:pathPrefix="/com.easycura/authentication_return"
252                    android:scheme="stripe-auth" />
252-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:19-40
253
254                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
255                <data
255-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:13-43
256                    android:host="link-native-accounts"
257                    android:pathPrefix="/com.easycura/authentication_return"
258                    android:scheme="stripe-auth" />
258-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:19-40
259
260                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
261                <data
261-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:13-43
262                    android:host="link-accounts"
263                    android:path="/com.easycura/success"
264                    android:scheme="stripe-auth" />
264-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:19-40
265                <data
265-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:13-43
266                    android:host="link-accounts"
267                    android:path="/com.easycura/cancel"
268                    android:scheme="stripe-auth" />
268-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:19-40
269
270                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
271                <data
271-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:13-43
272                    android:host="native-redirect"
273                    android:pathPrefix="/com.easycura"
274                    android:scheme="stripe-auth" />
274-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:19-40
275
276                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
277                <data
277-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:13-43
278                    android:host="auth-redirect"
279                    android:pathPrefix="/com.easycura"
280                    android:scheme="stripe" />
280-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:19-40
281            </intent-filter>
282        </activity>
283        <activity
283-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:66:9-69:77
284            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
284-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:67:13-101
285            android:exported="false"
285-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:68:13-37
286            android:theme="@style/StripeFinancialConnectionsDefaultTheme" />
286-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:69:13-74
287        <activity
287-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:70:9-74:58
288            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
288-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:71:13-110
289            android:exported="false"
289-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:72:13-37
290            android:theme="@style/StripeFinancialConnectionsDefaultTheme"
290-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:73:13-74
291            android:windowSoftInputMode="adjustResize" />
291-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:74:13-55
292        <activity
292-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:8:9-11:69
293            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
293-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:9:13-80
294            android:exported="false"
294-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:10:13-37
295            android:theme="@style/StripePaymentSheetDefaultTheme" />
295-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:11:13-66
296        <activity
296-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:12:9-15:69
297            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
297-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:13:13-82
298            android:exported="false"
298-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:14:13-37
299            android:theme="@style/StripePaymentSheetDefaultTheme" />
299-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:15:13-66
300        <activity
300-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:16:9-19:69
301            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
301-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:17:13-82
302            android:exported="false"
302-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:18:13-37
303            android:theme="@style/StripePaymentSheetDefaultTheme" />
303-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:19:13-66
304        <activity
304-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:20:9-23:69
305            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
305-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:21:13-97
306            android:exported="false"
306-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:22:13-37
307            android:theme="@style/StripePaymentSheetDefaultTheme" />
307-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:23:13-66
308        <activity
308-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:24:9-27:69
309            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
309-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:25:13-118
310            android:exported="false"
310-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:26:13-37
311            android:theme="@style/StripePaymentSheetDefaultTheme" />
311-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:27:13-66
312        <activity
312-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:28:9-31:69
313            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
313-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:29:13-105
314            android:exported="false"
314-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:30:13-37
315            android:theme="@style/StripePaymentSheetDefaultTheme" />
315-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:31:13-66
316        <activity
316-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:32:9-35:69
317            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
317-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:33:13-82
318            android:exported="false"
318-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:34:13-37
319            android:theme="@style/StripePaymentSheetDefaultTheme" />
319-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:35:13-66
320        <activity
320-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:36:9-39:68
321            android:name="com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity"
321-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:37:13-94
322            android:exported="false"
322-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:38:13-37
323            android:theme="@style/StripePayLauncherDefaultTheme" />
323-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:39:13-65
324        <activity
324-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:40:9-42:69
325            android:name="com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity"
325-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:41:13-121
326            android:theme="@style/StripePaymentSheetDefaultTheme" />
326-->[com.stripe:paymentsheet:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082484f18a25899531f24341875f44f\transformed\jetified-paymentsheet-20.52.3\AndroidManifest.xml:42:13-66
327        <activity
327-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:8:9-13:61
328            android:name="com.stripe.android.link.LinkActivity"
328-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:9:13-64
329            android:autoRemoveFromRecents="true"
329-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:10:13-49
330            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
330-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:11:13-115
331            android:launchMode="singleTop"
331-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:12:13-43
332            android:theme="@style/StripeTransparentTheme" />
332-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:13:13-58
333        <activity
333-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:14:9-19:61
334            android:name="com.stripe.android.link.LinkForegroundActivity"
334-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:15:13-74
335            android:autoRemoveFromRecents="true"
335-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:16:13-49
336            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
336-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:17:13-115
337            android:launchMode="singleTop"
337-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:18:13-43
338            android:theme="@style/StripeTransparentTheme" />
338-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:19:13-58
339        <activity
339-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:20:9-37:20
340            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
340-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:21:13-79
341            android:autoRemoveFromRecents="true"
341-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:22:13-49
342            android:exported="true"
342-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:23:13-36
343            android:launchMode="singleInstance"
343-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:24:13-48
344            android:theme="@style/StripeTransparentTheme" >
344-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:25:13-58
345            <intent-filter>
345-->[com.stripe:link:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\36adc2af64aefa6b9bce8f8f0ab0eca2\transformed\jetified-link-20.52.3\AndroidManifest.xml:26:13-36:29
346                <action android:name="android.intent.action.VIEW" />
346-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:14:13-65
346-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:14:21-62
347
348                <category android:name="android.intent.category.DEFAULT" />
348-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:28:17-76
348-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:28:27-73
349                <category android:name="android.intent.category.BROWSABLE" />
349-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:29:17-78
349-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:29:27-75
350
351                <data
351-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:13-43
352                    android:host="complete"
353                    android:path="/com.easycura"
354                    android:scheme="link-popup" />
354-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:19-40
355            </intent-filter>
356        </activity>
357        <activity
357-->[com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9cce331ffb64663587db3c3239c1f\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:8:9-11:69
358            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
358-->[com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9cce331ffb64663587db3c3239c1f\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:9:13-80
359            android:exported="false"
359-->[com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9cce331ffb64663587db3c3239c1f\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:10:13-37
360            android:theme="@style/StripePaymentSheetDefaultTheme" />
360-->[com.stripe:payments-ui-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec9cce331ffb64663587db3c3239c1f\transformed\jetified-payments-ui-core-20.52.3\AndroidManifest.xml:11:13-66
361        <activity
361-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:15:9-18:57
362            android:name="com.stripe.android.view.AddPaymentMethodActivity"
362-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:16:13-76
363            android:exported="false"
363-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:17:13-37
364            android:theme="@style/StripeDefaultTheme" />
364-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:18:13-54
365        <activity
365-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:19:9-22:57
366            android:name="com.stripe.android.view.PaymentMethodsActivity"
366-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:20:13-74
367            android:exported="false"
367-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:21:13-37
368            android:theme="@style/StripeDefaultTheme" />
368-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:22:13-54
369        <activity
369-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:23:9-26:57
370            android:name="com.stripe.android.view.PaymentFlowActivity"
370-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:24:13-71
371            android:exported="false"
371-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:25:13-37
372            android:theme="@style/StripeDefaultTheme" />
372-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:26:13-54
373        <activity
373-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:27:9-30:57
374            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
374-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:28:13-78
375            android:exported="false"
375-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:29:13-37
376            android:theme="@style/StripeDefaultTheme" />
376-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:30:13-54
377        <activity
377-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:31:9-34:61
378            android:name="com.stripe.android.view.PaymentRelayActivity"
378-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:32:13-72
379            android:exported="false"
379-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:33:13-37
380            android:theme="@style/StripeTransparentTheme" />
380-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:34:13-58
381        <!--
382        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
383        launched the browser Activity will also handle the return URL deep link.
384        -->
385        <activity
385-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:40:9-44:61
386            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
386-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:41:13-85
387            android:exported="false"
387-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:42:13-37
388            android:launchMode="singleTask"
388-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:43:13-44
389            android:theme="@style/StripeTransparentTheme" />
389-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:44:13-58
390        <activity
390-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:45:9-62:20
391            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
391-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:46:13-88
392            android:exported="true"
392-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:47:13-36
393            android:launchMode="singleTask"
393-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:48:13-44
394            android:theme="@style/StripeTransparentTheme" >
394-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:49:13-58
395            <intent-filter>
395-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:50:13-61:29
396                <action android:name="android.intent.action.VIEW" />
396-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:14:13-65
396-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:14:21-62
397
398                <category android:name="android.intent.category.DEFAULT" />
398-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:28:17-76
398-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:28:27-73
399                <category android:name="android.intent.category.BROWSABLE" />
399-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:29:17-78
399-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:29:27-75
400
401                <!-- Must match `DefaultReturnUrl#value`. -->
402                <data
402-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:13-43
403                    android:host="payment_return_url"
404                    android:path="/com.easycura"
405                    android:scheme="stripesdk" />
405-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:19-40
406            </intent-filter>
407        </activity>
408        <activity
408-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:63:9-66:57
409            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
409-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:64:13-114
410            android:exported="false"
410-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:65:13-37
411            android:theme="@style/StripeDefaultTheme" />
411-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:66:13-54
412        <activity
412-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:67:9-70:66
413            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
413-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:68:13-90
414            android:exported="false"
414-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:69:13-37
415            android:theme="@style/StripeGooglePayDefaultTheme" />
415-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:70:13-63
416        <activity
416-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:71:9-74:66
417            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
417-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:72:13-103
418            android:exported="false"
418-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:73:13-37
419            android:theme="@style/StripeGooglePayDefaultTheme" />
419-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:74:13-63
420        <activity
420-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:75:9-78:68
421            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
421-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:76:13-107
422            android:exported="false"
422-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:77:13-37
423            android:theme="@style/StripePayLauncherDefaultTheme" />
423-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:78:13-65
424        <activity
424-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:79:9-82:61
425            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
425-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:80:13-97
426            android:exported="false"
426-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:81:13-37
427            android:theme="@style/StripeTransparentTheme" />
427-->[com.stripe:payments-core:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82871071b53c44fd943fe85dfe67db1c\transformed\jetified-payments-core-20.52.3\AndroidManifest.xml:82:13-58
428        <activity
428-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61660682525456ea1e61825c06c8a332\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:8:9-11:54
429            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
429-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61660682525456ea1e61825c06c8a332\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:9:13-81
430            android:exported="false"
430-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61660682525456ea1e61825c06c8a332\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:10:13-37
431            android:theme="@style/Stripe3DS2Theme" />
431-->[com.stripe:stripe-3ds2-android:6.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\61660682525456ea1e61825c06c8a332\transformed\jetified-stripe-3ds2-android-6.1.8\AndroidManifest.xml:11:13-51
432
433        <meta-data
433-->[com.google.maps.android:android-maps-utils:3.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52c8b1235080f34adfa9f8a8d6897eed\transformed\jetified-android-maps-utils-3.10.0\AndroidManifest.xml:23:9-25:69
434            android:name="com.google.android.gms.version"
434-->[com.google.maps.android:android-maps-utils:3.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52c8b1235080f34adfa9f8a8d6897eed\transformed\jetified-android-maps-utils-3.10.0\AndroidManifest.xml:24:13-58
435            android:value="@integer/google_play_services_version" />
435-->[com.google.maps.android:android-maps-utils:3.10.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52c8b1235080f34adfa9f8a8d6897eed\transformed\jetified-android-maps-utils-3.10.0\AndroidManifest.xml:25:13-66
436        <meta-data
436-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:19:9-21:37
437            android:name="com.google.android.play.billingclient.version"
437-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:20:13-73
438            android:value="7.0.0" />
438-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:21:13-34
439
440        <activity
440-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:23:9-27:75
441            android:name="com.android.billingclient.api.ProxyBillingActivity"
441-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:24:13-78
442            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
442-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:25:13-96
443            android:exported="false"
443-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:26:13-37
444            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
444-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:27:13-72
445        <activity
445-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:28:9-32:75
446            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
446-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:29:13-80
447            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
447-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:30:13-96
448            android:exported="false"
448-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:31:13-37
449            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
449-->[com.android.billingclient:billing:7.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf2cb38e95176247ef4f75153baf69e7\transformed\jetified-billing-7.0.0\AndroidManifest.xml:32:13-72
450        <activity
450-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:29:9-46:20
451            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
451-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:30:13-80
452            android:excludeFromRecents="true"
452-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:31:13-46
453            android:exported="true"
453-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:32:13-36
454            android:launchMode="singleTask"
454-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:33:13-44
455            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
455-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:34:13-72
456            <intent-filter>
456-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:35:13-45:29
457                <action android:name="android.intent.action.VIEW" />
457-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:14:13-65
457-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:14:21-62
458
459                <category android:name="android.intent.category.DEFAULT" />
459-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:28:17-76
459-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:28:27-73
460                <category android:name="android.intent.category.BROWSABLE" />
460-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:29:17-78
460-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:29:27-75
461
462                <data
462-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:13-43
463                    android:host="firebase.auth"
464                    android:path="/"
465                    android:scheme="genericidp" />
465-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:19-40
466            </intent-filter>
467        </activity>
468        <activity
468-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:47:9-64:20
469            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
469-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:48:13-79
470            android:excludeFromRecents="true"
470-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:49:13-46
471            android:exported="true"
471-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:50:13-36
472            android:launchMode="singleTask"
472-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:51:13-44
473            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
473-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:52:13-72
474            <intent-filter>
474-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf4b19679b7213f3075524216dfc27a\transformed\jetified-firebase-auth-23.0.0\AndroidManifest.xml:53:13-63:29
475                <action android:name="android.intent.action.VIEW" />
475-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:14:13-65
475-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:14:21-62
476
477                <category android:name="android.intent.category.DEFAULT" />
477-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:28:17-76
477-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:28:27-73
478                <category android:name="android.intent.category.BROWSABLE" />
478-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:29:17-78
478-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:29:27-75
479
480                <data
480-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:13-43
481                    android:host="firebase.auth"
482                    android:path="/"
483                    android:scheme="recaptcha" />
483-->[com.stripe:financial-connections:20.52.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83f7551692db870252b847c78ea74fe8\transformed\jetified-financial-connections-20.52.3\AndroidManifest.xml:16:19-40
484            </intent-filter>
485        </activity>
486
487        <service
487-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
488            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
488-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
489            android:enabled="true"
489-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
490            android:exported="false" >
490-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
491            <meta-data
491-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
492                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
492-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
493                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
493-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
494        </service>
495
496        <activity
496-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
497            android:name="androidx.credentials.playservices.HiddenActivity"
497-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
498            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
498-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
499            android:enabled="true"
499-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
500            android:exported="false"
500-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
501            android:fitsSystemWindows="true"
501-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
502            android:theme="@style/Theme.Hidden" >
502-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63995221398baf5782e67e5e42b1ff36\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
503        </activity>
504        <activity
504-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
505            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
505-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
506            android:excludeFromRecents="true"
506-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
507            android:exported="false"
507-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
508            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
508-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
509        <!--
510            Service handling Google Sign-In user revocation. For apps that do not integrate with
511            Google Sign-In, this service will never be started.
512        -->
513        <service
513-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
514            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
514-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
515            android:exported="true"
515-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
516            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
516-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
517            android:visibleToInstantApps="true" /> <!-- Needs to be explicitly declared on P+ -->
517-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be4ab5a44ecb5562e7bbd9d803b97366\transformed\jetified-play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
518        <uses-library
518-->[com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:33:7-86
519            android:name="org.apache.http.legacy"
519-->[com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:33:21-58
520            android:required="false" />
520-->[com.google.android.gms:play-services-maps:19.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ad8e98c43687a3d36e1788821c31dace\transformed\jetified-play-services-maps-19.1.0\AndroidManifest.xml:33:59-83
521
522        <receiver
522-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:29:9-40:20
523            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
523-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:30:13-78
524            android:exported="true"
524-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:31:13-36
525            android:permission="com.google.android.c2dm.permission.SEND" >
525-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:32:13-73
526            <intent-filter>
526-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:33:13-35:29
527                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
527-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:34:17-81
527-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:34:25-78
528            </intent-filter>
529
530            <meta-data
530-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:37:13-39:40
531                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
531-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:38:17-92
532                android:value="true" />
532-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:39:17-37
533        </receiver>
534        <!--
535             FirebaseMessagingService performs security checks at runtime,
536             but set to not exported to explicitly avoid allowing another app to call it.
537        -->
538        <service
538-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:46:9-53:19
539            android:name="com.google.firebase.messaging.FirebaseMessagingService"
539-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:47:13-82
540            android:directBootAware="true"
540-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:48:13-43
541            android:exported="false" >
541-->[com.google.firebase:firebase-messaging:24.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e7ddeb57dd9e5d0d13f434ec1bce5b59\transformed\jetified-firebase-messaging-24.0.0\AndroidManifest.xml:49:13-37
542            <intent-filter android:priority="-500" >
542-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:67:13-69:29
543                <action android:name="com.google.firebase.MESSAGING_EVENT" />
543-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:68:17-77
543-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:68:25-75
544            </intent-filter>
545        </service>
546
547        <activity
547-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0aca4c1084c580fd84a1e342864f2bf\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
548            android:name="com.google.android.gms.common.api.GoogleApiActivity"
548-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0aca4c1084c580fd84a1e342864f2bf\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
549            android:exported="false"
549-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0aca4c1084c580fd84a1e342864f2bf\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
550            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
550-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0aca4c1084c580fd84a1e342864f2bf\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
551
552        <property
552-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:30:9-32:61
553            android:name="android.adservices.AD_SERVICES_CONFIG"
553-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:31:13-65
554            android:resource="@xml/ga_ad_services_config" />
554-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0dd4085976d77a37c80687b5e38f1ff\transformed\jetified-play-services-measurement-api-22.0.2\AndroidManifest.xml:32:13-58
555
556        <provider
556-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
557            android:name="com.google.firebase.provider.FirebaseInitProvider"
557-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
558            android:authorities="com.easycura.firebaseinitprovider"
558-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
559            android:directBootAware="true"
559-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
560            android:exported="false"
560-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
561            android:initOrder="100" />
561-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf8bf780bf9a753394a975e8217e446f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
562        <provider
562-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:29:9-37:20
563            android:name="androidx.startup.InitializationProvider"
563-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:30:13-67
564            android:authorities="com.easycura.androidx-startup"
564-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:31:13-68
565            android:exported="false" >
565-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:32:13-37
566            <meta-data
566-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:34:13-36:52
567                android:name="androidx.work.WorkManagerInitializer"
567-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:35:17-68
568                android:value="androidx.startup" />
568-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:36:17-49
569            <meta-data
569-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f9e75e8f31982799eee1bae3519949\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
570                android:name="androidx.emoji2.text.EmojiCompatInitializer"
570-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f9e75e8f31982799eee1bae3519949\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
571                android:value="androidx.startup" />
571-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f9e75e8f31982799eee1bae3519949\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
572            <meta-data
572-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c91034905ca39efe69a659a8881112d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
573                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
573-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c91034905ca39efe69a659a8881112d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
574                android:value="androidx.startup" />
574-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c91034905ca39efe69a659a8881112d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
575            <meta-data
575-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
576                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
576-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
577                android:value="androidx.startup" />
577-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
578        </provider>
579
580        <service
580-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:39:9-45:35
581            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
581-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:40:13-88
582            android:directBootAware="false"
582-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:41:13-44
583            android:enabled="@bool/enable_system_alarm_service_default"
583-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:42:13-72
584            android:exported="false" />
584-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:43:13-37
585        <service
585-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:46:9-52:35
586            android:name="androidx.work.impl.background.systemjob.SystemJobService"
586-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:47:13-84
587            android:directBootAware="false"
587-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:48:13-44
588            android:enabled="@bool/enable_system_job_service_default"
588-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:49:13-70
589            android:exported="true"
589-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:50:13-36
590            android:permission="android.permission.BIND_JOB_SERVICE" />
590-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:51:13-69
591        <service
591-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:53:9-59:35
592            android:name="androidx.work.impl.foreground.SystemForegroundService"
592-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:54:13-81
593            android:directBootAware="false"
593-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:55:13-44
594            android:enabled="@bool/enable_system_foreground_service_default"
594-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:56:13-77
595            android:exported="false" />
595-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:57:13-37
596
597        <receiver
597-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:61:9-66:35
598            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
598-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:62:13-88
599            android:directBootAware="false"
599-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:63:13-44
600            android:enabled="true"
600-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:64:13-35
601            android:exported="false" />
601-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:65:13-37
602        <receiver
602-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:67:9-77:20
603            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
603-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:68:13-106
604            android:directBootAware="false"
604-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:69:13-44
605            android:enabled="false"
605-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:70:13-36
606            android:exported="false" >
606-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:71:13-37
607            <intent-filter>
607-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:73:13-76:29
608                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
608-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:17-87
608-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:25-84
609                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
609-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:17-90
609-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:25-87
610            </intent-filter>
611        </receiver>
612        <receiver
612-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:78:9-88:20
613            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
613-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:79:13-104
614            android:directBootAware="false"
614-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:80:13-44
615            android:enabled="false"
615-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:81:13-36
616            android:exported="false" >
616-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:82:13-37
617            <intent-filter>
617-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:84:13-87:29
618                <action android:name="android.intent.action.BATTERY_OKAY" />
618-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:17-77
618-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:25-74
619                <action android:name="android.intent.action.BATTERY_LOW" />
619-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:17-76
619-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:25-73
620            </intent-filter>
621        </receiver>
622        <receiver
622-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:89:9-99:20
623            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
623-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:90:13-104
624            android:directBootAware="false"
624-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:91:13-44
625            android:enabled="false"
625-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:92:13-36
626            android:exported="false" >
626-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:93:13-37
627            <intent-filter>
627-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:95:13-98:29
628                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
628-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:17-83
628-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:25-80
629                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
629-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:17-82
629-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:25-79
630            </intent-filter>
631        </receiver>
632        <receiver
632-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:100:9-109:20
633            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
633-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:101:13-103
634            android:directBootAware="false"
634-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:102:13-44
635            android:enabled="false"
635-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:103:13-36
636            android:exported="false" >
636-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:104:13-37
637            <intent-filter>
637-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:106:13-108:29
638                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
638-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:17-79
638-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:25-76
639            </intent-filter>
640        </receiver>
641        <receiver
641-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:110:9-121:20
642            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
642-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:111:13-88
643            android:directBootAware="false"
643-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:112:13-44
644            android:enabled="false"
644-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:113:13-36
645            android:exported="false" >
645-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:114:13-37
646            <intent-filter>
646-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:116:13-120:29
647                <action android:name="android.intent.action.BOOT_COMPLETED" />
647-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:60:17-78
647-->D:\Projects\EasyCura\frontend\android\app\src\main\AndroidManifest.xml:60:25-76
648                <action android:name="android.intent.action.TIME_SET" />
648-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:17-73
648-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:25-70
649                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
649-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:17-81
649-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:25-78
650            </intent-filter>
651        </receiver>
652        <receiver
652-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:122:9-131:20
653            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
653-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:123:13-99
654            android:directBootAware="false"
654-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:124:13-44
655            android:enabled="@bool/enable_system_alarm_service_default"
655-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:125:13-72
656            android:exported="false" >
656-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:126:13-37
657            <intent-filter>
657-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:128:13-130:29
658                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
658-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:17-98
658-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:25-95
659            </intent-filter>
660        </receiver>
661        <receiver
661-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:132:9-142:20
662            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
662-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:133:13-78
663            android:directBootAware="false"
663-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:134:13-44
664            android:enabled="true"
664-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:135:13-35
665            android:exported="true"
665-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:136:13-36
666            android:permission="android.permission.DUMP" >
666-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:137:13-57
667            <intent-filter>
667-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:139:13-141:29
668                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
668-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:17-88
668-->[androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5ab4d954d1e20aa5ec3ca567a7a4ee3\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:25-85
669            </intent-filter>
670        </receiver> <!-- 'android:authorities' must be unique in the device, across all apps -->
671        <provider
671-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:12:9-15:40
672            android:name="io.sentry.android.core.SentryInitProvider"
672-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:13:13-69
673            android:authorities="com.easycura.SentryInitProvider"
673-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:14:13-70
674            android:exported="false" />
674-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:15:13-37
675        <provider
675-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:16:9-20:39
676            android:name="io.sentry.android.core.SentryPerformanceProvider"
676-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:17:13-76
677            android:authorities="com.easycura.SentryPerformanceProvider"
677-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:18:13-77
678            android:exported="false"
678-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:19:13-37
679            android:initOrder="200" />
679-->[io.sentry:sentry-android-core:7.22.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57595b3cb961835e486201cafaba4be4\transformed\jetified-sentry-android-core-7.22.5\AndroidManifest.xml:20:13-36
680
681        <receiver
681-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:29:9-33:20
682            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
682-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:30:13-85
683            android:enabled="true"
683-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:31:13-35
684            android:exported="false" >
684-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:32:13-37
685        </receiver>
686
687        <service
687-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:35:9-38:40
688            android:name="com.google.android.gms.measurement.AppMeasurementService"
688-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:36:13-84
689            android:enabled="true"
689-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:37:13-35
690            android:exported="false" />
690-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:38:13-37
691        <service
691-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:39:9-43:72
692            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
692-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:40:13-87
693            android:enabled="true"
693-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:41:13-35
694            android:exported="false"
694-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:42:13-37
695            android:permission="android.permission.BIND_JOB_SERVICE" />
695-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02a38dc4a5de070e6d587318394a838\transformed\jetified-play-services-measurement-22.0.2\AndroidManifest.xml:43:13-69
696
697        <uses-library
697-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1df0ef958be93b2f6a07daeefeadec68\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
698            android:name="android.ext.adservices"
698-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1df0ef958be93b2f6a07daeefeadec68\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
699            android:required="false" />
699-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1df0ef958be93b2f6a07daeefeadec68\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
700
701        <service
701-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dc46601abed3bc5f006f747cfcd35d8\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
702            android:name="androidx.room.MultiInstanceInvalidationService"
702-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dc46601abed3bc5f006f747cfcd35d8\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
703            android:directBootAware="true"
703-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dc46601abed3bc5f006f747cfcd35d8\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
704            android:exported="false" />
704-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8dc46601abed3bc5f006f747cfcd35d8\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
705
706        <receiver
706-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
707            android:name="androidx.profileinstaller.ProfileInstallReceiver"
707-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
708            android:directBootAware="false"
708-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
709            android:enabled="true"
709-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
710            android:exported="true"
710-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
711            android:permission="android.permission.DUMP" >
711-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
712            <intent-filter>
712-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
713                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
713-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
713-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
714            </intent-filter>
715            <intent-filter>
715-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
716                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
716-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
716-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
717            </intent-filter>
718            <intent-filter>
718-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
719                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
719-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
719-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
720            </intent-filter>
721            <intent-filter>
721-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
722                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
722-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
722-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c6fa0a9dce1f5a933b70fb0fd5949b7f\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
723            </intent-filter>
724        </receiver>
725
726        <service
726-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
727            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
727-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
728            android:exported="false" >
728-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
729            <meta-data
729-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
730                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
730-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
731                android:value="cct" />
731-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a78f470cdd9f8b4347a57f9dc0f38538\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
732        </service>
733        <service
733-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
734            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
734-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
735            android:exported="false"
735-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
736            android:permission="android.permission.BIND_JOB_SERVICE" >
736-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
737        </service>
738
739        <receiver
739-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
740            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
740-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
741            android:exported="false" />
741-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f83e0f3459e6126c998e177a73db4dcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
742
743        <meta-data
743-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b198ecf16b59ae0079af471be112cc6b\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:12:9-14:37
744            android:name="com.facebook.soloader.enabled"
744-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b198ecf16b59ae0079af471be112cc6b\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:13:13-57
745            android:value="false" />
745-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b198ecf16b59ae0079af471be112cc6b\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:14:13-34
746        <meta-data
746-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9fa070465237736c228043f51912a60\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
747            android:name="aia-compat-api-min-version"
747-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9fa070465237736c228043f51912a60\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
748            android:value="1" />
748-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9fa070465237736c228043f51912a60\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
749    </application>
750
751</manifest>
