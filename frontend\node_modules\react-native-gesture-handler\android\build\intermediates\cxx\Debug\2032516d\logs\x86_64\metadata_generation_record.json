[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86_64", "file_": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\2032516d\\x86_64\\android_gradle_build.json' was up-to-date", "file_": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]