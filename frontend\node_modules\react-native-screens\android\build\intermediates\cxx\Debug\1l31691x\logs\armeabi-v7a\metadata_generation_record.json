[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: armeabi-v7a", "file_": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-screens\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1l31691x\\armeabi-v7a\\android_gradle_build.json' was up-to-date", "file_": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-screens\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Projects\\EasyCura\\frontend\\node_modules\\react-native-screens\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]